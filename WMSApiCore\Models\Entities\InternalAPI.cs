using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMSApiCore.Models.Entities;

/// <summary>
/// 内部API映射表
/// </summary>
[Table("InternalAPI")]
public class InternalAPI
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 访问路径
    /// </summary>
    [StringLength(128)]
    public string AccessPath { get; set; } = string.Empty;

    /// <summary>
    /// HTTP方法
    /// </summary>
    [StringLength(4)]
    public string HttpMethod { get; set; } = string.Empty;

    /// <summary>
    /// 参数
    /// </summary>
    [StringLength(255)]
    public string Parameters { get; set; } = string.Empty;

    /// <summary>
    /// 映射函数
    /// </summary>
    [StringLength(40)]
    public string MappingFunction { get; set; } = string.Empty;

    /// <summary>
    /// 函数权限
    /// </summary>
    [StringLength(40)]
    public string FunctionPermission { get; set; } = string.Empty;

    /// <summary>
    /// 是否需要认证
    /// </summary>
    public bool IsAuthRequired { get; set; } = true;

    /// <summary>
    /// 是否异步函数
    /// </summary>
    public bool IsAsyncFunc { get; set; } = false;

    /// <summary>
    /// 是否已废弃
    /// </summary>
    public bool IsAbandoned { get; set; } = false;
}
