using System.Security.Cryptography;
using System.Text;

namespace WMSApiCore.Services;

/// <summary>
/// 加密服务
/// </summary>
public class EncryptionService
{
    private readonly ILogger<EncryptionService> _logger;

    public EncryptionService(ILogger<EncryptionService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 加密文本（模拟原VB.NET的EncryptText函数）
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="key">密钥</param>
    /// <param name="salt">盐值</param>
    /// <returns>加密后的文本</returns>
    public string EncryptText(string plainText, string key, string salt)
    {
        try
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            // 使用密钥和盐值生成派生密钥
            var keyBytes = Encoding.UTF8.GetBytes(key + salt);
            var plainBytes = Encoding.UTF8.GetBytes(plainText);

            // 使用SHA256生成固定长度的密钥
            using var sha256 = SHA256.Create();
            var hashKey = sha256.ComputeHash(keyBytes);

            // 使用AES加密
            using var aes = Aes.Create();
            aes.Key = hashKey;
            aes.Mode = CipherMode.ECB; // 使用ECB模式以保持与原系统兼容
            aes.Padding = PaddingMode.PKCS7;

            using var encryptor = aes.CreateEncryptor();
            var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);

            // 转换为Base64字符串
            return Convert.ToBase64String(encryptedBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加密文本时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 解密文本
    /// </summary>
    /// <param name="encryptedText">加密文本</param>
    /// <param name="key">密钥</param>
    /// <param name="salt">盐值</param>
    /// <returns>解密后的文本</returns>
    public string DecryptText(string encryptedText, string key, string salt)
    {
        try
        {
            if (string.IsNullOrEmpty(encryptedText))
                return string.Empty;

            // 使用密钥和盐值生成派生密钥
            var keyBytes = Encoding.UTF8.GetBytes(key + salt);
            var encryptedBytes = Convert.FromBase64String(encryptedText);

            // 使用SHA256生成固定长度的密钥
            using var sha256 = SHA256.Create();
            var hashKey = sha256.ComputeHash(keyBytes);

            // 使用AES解密
            using var aes = Aes.Create();
            aes.Key = hashKey;
            aes.Mode = CipherMode.ECB;
            aes.Padding = PaddingMode.PKCS7;

            using var decryptor = aes.CreateDecryptor();
            var decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);

            return Encoding.UTF8.GetString(decryptedBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解密文本时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="plainPassword">明文密码</param>
    /// <param name="encryptedPassword">加密密码</param>
    /// <param name="key">密钥</param>
    /// <param name="salt">盐值</param>
    /// <returns>是否匹配</returns>
    public bool VerifyPassword(string plainPassword, string encryptedPassword, string key, string salt)
    {
        try
        {
            var encrypted = EncryptText(plainPassword, key, salt);
            return encrypted == encryptedPassword;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证密码时发生错误");
            return false;
        }
    }

    /// <summary>
    /// 生成随机盐值
    /// </summary>
    /// <param name="length">长度</param>
    /// <returns>盐值</returns>
    public string GenerateSalt(int length = 16)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    /// <summary>
    /// 计算MD5哈希
    /// </summary>
    /// <param name="input">输入文本</param>
    /// <returns>MD5哈希值</returns>
    public string ComputeMD5Hash(string input)
    {
        try
        {
            using var md5 = MD5.Create();
            var inputBytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = md5.ComputeHash(inputBytes);
            
            var sb = new StringBuilder();
            foreach (var b in hashBytes)
            {
                sb.Append(b.ToString("x2"));
            }
            
            return sb.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算MD5哈希时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 计算SHA256哈希
    /// </summary>
    /// <param name="input">输入文本</param>
    /// <returns>SHA256哈希值</returns>
    public string ComputeSHA256Hash(string input)
    {
        try
        {
            using var sha256 = SHA256.Create();
            var inputBytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = sha256.ComputeHash(inputBytes);
            
            return Convert.ToBase64String(hashBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算SHA256哈希时发生错误");
            throw;
        }
    }
}
