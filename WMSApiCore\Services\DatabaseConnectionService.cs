using System.Net.NetworkInformation;
using System.Text.Json;
using WMSApiCore.Models.Configuration;

namespace WMSApiCore.Services;

/// <summary>
/// 数据库连接服务
/// </summary>
public class DatabaseConnectionService
{
    private readonly ILogger<DatabaseConnectionService> _logger;
    private readonly string _projectPath;

    public DatabaseConnectionService(ILogger<DatabaseConnectionService> logger, IWebHostEnvironment environment)
    {
        _logger = logger;
        _projectPath = environment.ContentRootPath;
    }

    /// <summary>
    /// 获取数据库连接字符串
    /// </summary>
    /// <param name="connectionName">连接名称（sys或log）</param>
    /// <returns>连接字符串</returns>
    public async Task<string> GetConnectionStringAsync(string connectionName = "sys")
    {
        try
        {
            // 读取配置文件
            var filePath = Path.Combine(_projectPath, "sqlcfg.json");
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("配置文件不存在，请检查！");
            }

            var jsonStr = await File.ReadAllTextAsync(filePath);
            var config = JsonSerializer.Deserialize<DatabaseConfig>(jsonStr, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (config == null)
            {
                throw new InvalidOperationException("配置文件内容错误，请检查！");
            }

            string sqlName, sourceInternalIP, sourceInternalPort, sourceExternalIP, sourceExternalPort;

            if (connectionName.ToLower() == "log")
            {
                sqlName = config.LogSQLName;
                sourceInternalIP = config.LogSourceIP_Internal;
                sourceInternalPort = config.LogSourcePort_Internal;
                sourceExternalIP = config.LogSourceIP_External;
                sourceExternalPort = config.LogSourcePort_External;
            }
            else
            {
                sqlName = config.SQLName;
                sourceInternalIP = config.SourceIP_Internal;
                sourceInternalPort = config.SourcePort_Internal;
                sourceExternalIP = config.SourceIP_External;
                sourceExternalPort = config.SourcePort_External;
            }

            // 选择可用的服务器地址
            string serverAddress;
            if (await PingAsync(sourceInternalIP))
            {
                // 首选内网地址
                serverAddress = $"{sourceInternalIP},{sourceInternalPort}";
                _logger.LogInformation("使用内网数据库连接: {ServerAddress}", serverAddress);
            }
            else if (await PingAsync(sourceExternalIP))
            {
                // 次选外网地址
                serverAddress = $"{sourceExternalIP},{sourceExternalPort}";
                _logger.LogInformation("使用外网数据库连接: {ServerAddress}", serverAddress);
            }
            else
            {
                // 内外网均无法Ping通，抛出提示
                throw new InvalidOperationException("数据源连接异常，请检查网络及参数配置！");
            }

            // 构建连接字符串
            var connectionString = $"Server={serverAddress};Database={sqlName};User Id={config.Username};Password={config.Password};TrustServerCertificate=true;";
            
            return connectionString;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取数据库连接字符串失败");
            throw;
        }
    }

    /// <summary>
    /// Ping测试
    /// </summary>
    /// <param name="host">主机地址</param>
    /// <param name="timeout">超时时间（毫秒）</param>
    /// <returns>是否可达</returns>
    private async Task<bool> PingAsync(string host, int timeout = 1000)
    {
        try
        {
            using var ping = new Ping();
            var reply = await ping.SendPingAsync(host, timeout);
            return reply.Status == IPStatus.Success;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Ping {Host} 失败", host);
            return false;
        }
    }
}
