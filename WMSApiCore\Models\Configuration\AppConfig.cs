namespace WMSApiCore.Models.Configuration;

/// <summary>
/// 应用程序配置类
/// </summary>
public class AppConfig
{
    /// <summary>
    /// 数据库配置
    /// </summary>
    public DatabaseConfig Database { get; set; } = new();

    /// <summary>
    /// Web服务器配置
    /// </summary>
    public WebServerConfig WebServer { get; set; } = new();

    /// <summary>
    /// JWT配置
    /// </summary>
    public JwtConfig Jwt { get; set; } = new();

    /// <summary>
    /// 应用程序名称
    /// </summary>
    public string ApplicationName { get; set; } = "WMS API Core";

    /// <summary>
    /// 应用程序版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 是否启用开发模式
    /// </summary>
    public bool DevelopmentMode { get; set; } = false;
}
