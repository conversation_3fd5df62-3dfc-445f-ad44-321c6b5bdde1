using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMSApiCore.Models.Entities;

/// <summary>
/// 内部权限表
/// </summary>
[Table("InternalPermission")]
public class InternalPermission
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 路由ID
    /// </summary>
    [StringLength(36)]
    public string RouteID { get; set; } = string.Empty;

    /// <summary>
    /// 权限名称
    /// </summary>
    [StringLength(16)]
    public string PermName { get; set; } = string.Empty;

    /// <summary>
    /// 权限代码
    /// </summary>
    [StringLength(40)]
    public string PermCode { get; set; } = string.Empty;

    /// <summary>
    /// 是否查看权限
    /// </summary>
    public bool IsView { get; set; } = false;

    /// <summary>
    /// 是否删除
    /// </summary>
    public bool Del { get; set; } = false;
}
