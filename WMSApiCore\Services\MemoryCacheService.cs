using Microsoft.Extensions.Caching.Memory;
using Microsoft.EntityFrameworkCore;
using WMSApiCore.Data;
using WMSApiCore.Models.Entities;

namespace WMSApiCore.Services;

/// <summary>
/// 内存缓存服务
/// </summary>
public class MemoryCacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MemoryCacheService> _logger;

    // 缓存键常量
    private const string CACHE_KEY_INTERNAL_API = "InternalAPI";
    private const string CACHE_KEY_INTERNAL_PARAMETERS = "InternalParameters";
    private const string CACHE_KEY_INTERNAL_ROUTER = "InternalRouter";
    private const string CACHE_KEY_INTERNAL_PERMISSION = "InternalPermission";
    private const string CACHE_KEY_INTERNAL_API_CONDITION = "InternalApiCondition";

    // 缓存过期时间
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromHours(1);

    public MemoryCacheService(
        IMemoryCache memoryCache, 
        IServiceProvider serviceProvider,
        ILogger<MemoryCacheService> logger)
    {
        _memoryCache = memoryCache;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// 获取内部API映射数据
    /// </summary>
    /// <returns>API映射列表</returns>
    public async Task<List<InternalAPI>> GetInternalAPIsAsync()
    {
        return await _memoryCache.GetOrCreateAsync(CACHE_KEY_INTERNAL_API, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = _cacheExpiration;
            
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<WMSDbContext>();
            
            var data = await dbContext.InternalAPIs
                .Where(x => !x.IsAbandoned)
                .ToListAsync();
                
            _logger.LogInformation("已加载 {Count} 条内部API映射数据到缓存", data.Count);
            return data;
        }) ?? new List<InternalAPI>();
    }

    /// <summary>
    /// 获取内部参数数据
    /// </summary>
    /// <returns>参数列表</returns>
    public async Task<List<InternalParameters>> GetInternalParametersAsync()
    {
        return await _memoryCache.GetOrCreateAsync(CACHE_KEY_INTERNAL_PARAMETERS, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = _cacheExpiration;
            
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<WMSDbContext>();
            
            var data = await dbContext.InternalParameters
                .Where(x => x.IsActived)
                .OrderBy(x => x.SortKey)
                .ToListAsync();
                
            _logger.LogInformation("已加载 {Count} 条内部参数数据到缓存", data.Count);
            return data;
        }) ?? new List<InternalParameters>();
    }

    /// <summary>
    /// 获取内部路由数据
    /// </summary>
    /// <returns>路由列表</returns>
    public async Task<List<InternalRouter>> GetInternalRoutersAsync()
    {
        return await _memoryCache.GetOrCreateAsync(CACHE_KEY_INTERNAL_ROUTER, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = _cacheExpiration;
            
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<WMSDbContext>();
            
            var data = await dbContext.InternalRouters
                .Where(x => !x.Del)
                .OrderBy(x => x.Level)
                .ThenBy(x => x.MetaOrder)
                .ToListAsync();
                
            _logger.LogInformation("已加载 {Count} 条内部路由数据到缓存", data.Count);
            return data;
        }) ?? new List<InternalRouter>();
    }

    /// <summary>
    /// 获取内部权限数据
    /// </summary>
    /// <returns>权限列表</returns>
    public async Task<List<InternalPermission>> GetInternalPermissionsAsync()
    {
        return await _memoryCache.GetOrCreateAsync(CACHE_KEY_INTERNAL_PERMISSION, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = _cacheExpiration;
            
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<WMSDbContext>();
            
            var data = await dbContext.InternalPermissions
                .Where(x => !x.Del)
                .ToListAsync();
                
            _logger.LogInformation("已加载 {Count} 条内部权限数据到缓存", data.Count);
            return data;
        }) ?? new List<InternalPermission>();
    }

    /// <summary>
    /// 获取API条件数据
    /// </summary>
    /// <returns>API条件列表</returns>
    public async Task<List<InternalApiCondition>> GetInternalApiConditionsAsync()
    {
        return await _memoryCache.GetOrCreateAsync(CACHE_KEY_INTERNAL_API_CONDITION, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = _cacheExpiration;
            
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<WMSDbContext>();
            
            var data = await dbContext.InternalApiConditions.ToListAsync();
                
            _logger.LogInformation("已加载 {Count} 条API条件数据到缓存", data.Count);
            return data;
        }) ?? new List<InternalApiCondition>();
    }

    /// <summary>
    /// 根据参数类型获取参数
    /// </summary>
    /// <param name="parameterType">参数类型</param>
    /// <returns>参数列表</returns>
    public async Task<List<InternalParameters>> GetParametersByTypeAsync(string parameterType)
    {
        var allParameters = await GetInternalParametersAsync();
        return allParameters.Where(x => x.ParameterType == parameterType).ToList();
    }

    /// <summary>
    /// 根据访问路径获取API映射
    /// </summary>
    /// <param name="accessPath">访问路径</param>
    /// <param name="httpMethod">HTTP方法</param>
    /// <returns>API映射</returns>
    public async Task<InternalAPI?> GetAPIByPathAsync(string accessPath, string httpMethod)
    {
        var allAPIs = await GetInternalAPIsAsync();
        return allAPIs.FirstOrDefault(x => 
            x.AccessPath.Equals(accessPath, StringComparison.OrdinalIgnoreCase) && 
            x.HttpMethod.Equals(httpMethod, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 清除所有缓存
    /// </summary>
    public void ClearAllCache()
    {
        _memoryCache.Remove(CACHE_KEY_INTERNAL_API);
        _memoryCache.Remove(CACHE_KEY_INTERNAL_PARAMETERS);
        _memoryCache.Remove(CACHE_KEY_INTERNAL_ROUTER);
        _memoryCache.Remove(CACHE_KEY_INTERNAL_PERMISSION);
        _memoryCache.Remove(CACHE_KEY_INTERNAL_API_CONDITION);
        
        _logger.LogInformation("已清除所有内存缓存");
    }

    /// <summary>
    /// 清除指定缓存
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    public void ClearCache(string cacheKey)
    {
        _memoryCache.Remove(cacheKey);
        _logger.LogInformation("已清除缓存: {CacheKey}", cacheKey);
    }
}
