namespace WMSApiCore.Models.Configuration;

/// <summary>
/// 数据库配置类
/// </summary>
public class DatabaseConfig
{
    /// <summary>
    /// SQL Server数据库名称
    /// </summary>
    public string SQLName { get; set; } = string.Empty;

    /// <summary>
    /// 日志数据库名称
    /// </summary>
    public string LogSQLName { get; set; } = string.Empty;

    /// <summary>
    /// 内网IP地址
    /// </summary>
    public string SourceIP_Internal { get; set; } = string.Empty;

    /// <summary>
    /// 内网端口
    /// </summary>
    public string SourcePort_Internal { get; set; } = string.Empty;

    /// <summary>
    /// 外网IP地址
    /// </summary>
    public string SourceIP_External { get; set; } = string.Empty;

    /// <summary>
    /// 外网端口
    /// </summary>
    public string SourcePort_External { get; set; } = string.Empty;

    /// <summary>
    /// 数据库用户名
    /// </summary>
    public string Username { get; set; } = "tpit";

    /// <summary>
    /// 数据库密码
    /// </summary>
    public string Password { get; set; } = "Tpit#123";

    /// <summary>
    /// 日志数据库内网IP
    /// </summary>
    public string LogSourceIP_Internal { get; set; } = string.Empty;

    /// <summary>
    /// 日志数据库内网端口
    /// </summary>
    public string LogSourcePort_Internal { get; set; } = string.Empty;

    /// <summary>
    /// 日志数据库外网IP
    /// </summary>
    public string LogSourceIP_External { get; set; } = string.Empty;

    /// <summary>
    /// 日志数据库外网端口
    /// </summary>
    public string LogSourcePort_External { get; set; } = string.Empty;
}
