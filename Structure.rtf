{\rtf1\ansi\ansicpg936\deff0\deflang1033\deflangfe2052{\fonttbl{\f0\fnil\fcharset134 \'cb\'ce\'cc\'e5;}}
{\colortbl ;\red255\green0\blue0;\red0\green128\blue0;\red128\green128\blue128;\red0\green0\blue255;}
\viewkind4\uc1\pard\cf1\lang2052\f0\fs18\par
InternalAPI\par
\par
\pard\li600\cf2 AccessPath()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(128) \par
\cf2 HttpMethod()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(4) \par
\cf2 Parameters()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(255) \par
\cf2 MappingFunction()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(40) \par
\cf2 FunctionPermission()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(40) \par
\cf2 IsAuthRequired()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf2 IsAsyncFunc()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf2 IsAbandoned()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\pard\cf1\par
InternalApiCondition\par
\par
\pard\li600\cf2 WordType()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(16) \par
\cf2 WordName()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(32) \par
\cf2 MappingField()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(32) \par
\cf2 Operator()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(16) \par
\cf2 ApiPath()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(128) \par
\pard\cf1\par
InternalParameters\par
\par
\pard\li600\cf2 ParameterType()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(24) \par
\cf2 ParameterName()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(48) \par
\cf2 ParameterValue()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(64) \par
\cf2 Description()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(64) \par
\cf2 ParameterValueExt()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(16) \par
\cf2 IsActived()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf2 TagColor()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(24) \par
\cf2 IsDefault()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\pard\cf1\par
InternalPermission\par
\par
\pard\li600\cf2 RouteID()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(36) \par
\cf2 PermName()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(16) \par
\cf2 PermCode()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(40) \par
\cf2 IsView()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf2 Del()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\pard\cf1\par
InternalRouter\par
\par
\pard\li600\cf2 Guid()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(36) \par
\cf2 RouteName()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(32) \par
\cf2 Path()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(64) \par
\cf2 Redirect()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(64) \par
\cf2 Component()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(128) \par
\cf2 Level()\cf3  ----> \cf4\'d5\'fb\'ca\'fd \par
\cf2 ParentName()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(24) \par
\cf2 MetaActiveIcon()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(32) \par
\cf2 MetaActivePath()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(64) \par
\cf2 MetaHideChildrenInMenu()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf2 MetaHideInBreadcrumb()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf2 MetaHideInMenu()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf2 MetaHideInTab()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf2 MetaIcon()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(40) \par
\cf2 MetaKeepAlive()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf2 MetaLink()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(255) \par
\cf2 MetaOrder()\cf3  ----> \cf4\'d5\'fb\'ca\'fd \par
\cf2 MetaTitle()\cf3  ----> \cf4\'d7\'d6\'b7\'fb\'d0\'cd(20) \par
\cf2 Del()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf2 MetaAffixTab()\cf3  ----> \cf4\'c2\'df\'bc\'ad\'d0\'cd \par
\cf0\par
}
 