using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMSApiCore.Models.Entities;

/// <summary>
/// 系统用户表
/// </summary>
[Table("SysUser")]
public class SysUser
{
    /// <summary>
    /// 主键GUID
    /// </summary>
    [Key]
    [StringLength(36)]
    public string Guid { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    [StringLength(32)]
    public string UserID { get; set; } = string.Empty;

    /// <summary>
    /// 用户名
    /// </summary>
    [StringLength(64)]
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 加密密码
    /// </summary>
    [StringLength(128)]
    public string SecPW { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    [StringLength(128)]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 用户角色（JSON格式）
    /// </summary>
    [Column(TypeName = "nvarchar(max)")]
    public string UserRole { get; set; } = "[]";

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActived { get; set; } = true;

    /// <summary>
    /// 是否锁定
    /// </summary>
    public bool IsLocked { get; set; } = false;

    /// <summary>
    /// 最后锁定时间
    /// </summary>
    public DateTime? LastLockedDate { get; set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastLoginDate { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateDate { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否删除
    /// </summary>
    public bool Del { get; set; } = false;
}
