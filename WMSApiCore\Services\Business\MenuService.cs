using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using WMSApiCore.Data;
using WMSApiCore.Models.DTOs;

namespace WMSApiCore.Services.Business;

/// <summary>
/// 菜单业务服务
/// </summary>
public class MenuService
{
    private readonly WMSDbContext _dbContext;
    private readonly JwtService _jwtService;
    private readonly MemoryCacheService _cacheService;
    private readonly ILogger<MenuService> _logger;

    public MenuService(
        WMSDbContext dbContext,
        JwtService jwtService,
        MemoryCacheService cacheService,
        ILogger<MenuService> logger)
    {
        _dbContext = dbContext;
        _jwtService = jwtService;
        _cacheService = cacheService;
        _logger = logger;
    }

    /// <summary>
    /// API_MenuAll - 获取所有菜单
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="parameters">参数</param>
    /// <returns>菜单树</returns>
    public async Task<ApiResponse<JArray>> API_MenuAll(HttpContext context, Dictionary<string, object> parameters)
    {
        try
        {
            // 从JWT中获取用户GUID
            var userGuid = GetUserGuidFromContext(context);
            if (string.IsNullOrEmpty(userGuid))
            {
                context.Response.StatusCode = 401;
                return ApiResponse<JArray>.CreateError("未授权访问");
            }

            // 获取用户角色
            var user = await _dbContext.SysUsers
                .Where(u => !u.Del && u.Guid == userGuid)
                .FirstOrDefaultAsync();

            if (user == null)
            {
                context.Response.StatusCode = 401;
                return ApiResponse<JArray>.CreateError("用户不存在");
            }

            var roleJa = JArray.Parse(user.UserRole);

            // 获取用户角色中包含的所有路由名称
            var roles = await _dbContext.SysRoles
                .Where(r => !r.Del && r.IsActived)
                .ToListAsync();

            var routeNameJa = new JArray();
            routeNameJa.Add("Workspace"); // 默认添加工作台

            foreach (var roleId in roleJa)
            {
                var role = roles.FirstOrDefault(r => r.Guid == roleId.ToString());
                if (role != null)
                {
                    var roleRoutes = JArray.Parse(role.Route);
                    routeNameJa = new JArray(routeNameJa.Union(roleRoutes));
                }
            }

            // 添加父级路由
            for (int i = 0; i < routeNameJa.Count; i++)
            {
                AddParentRoute(routeNameJa, routeNameJa[i]!.ToString());
            }

            // 获取所有路由数据
            var allRouters = await _cacheService.GetInternalRoutersAsync();

            // 构建菜单树
            var routerJa = new JArray();
            var rootRouters = allRouters.Where(r => r.Level == 0 && !r.Del).ToList();

            foreach (var rootRouter in rootRouters)
            {
                var menuItem = HandleRoute(allRouters, routeNameJa, rootRouter);
                if (menuItem != null)
                {
                    routerJa.Add(menuItem);
                }
            }

            return ApiResponse<JArray>.CreateSuccess(routerJa, "获取菜单成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取菜单时发生错误");
            return ApiResponse<JArray>.CreateError("获取菜单失败");
        }
    }

    /// <summary>
    /// 添加父级路由
    /// </summary>
    /// <param name="routeNameJa">路由名称数组</param>
    /// <param name="routeName">路由名称</param>
    private async void AddParentRoute(JArray routeNameJa, string routeName)
    {
        var allRouters = await _cacheService.GetInternalRoutersAsync();
        var router = allRouters.FirstOrDefault(r => r.RouteName == routeName);
        
        if (router != null && !string.IsNullOrEmpty(router.ParentName))
        {
            if (!routeNameJa.Any(r => r.ToString() == router.ParentName))
            {
                routeNameJa.Add(router.ParentName);
                AddParentRoute(routeNameJa, router.ParentName);
            }
        }
    }

    /// <summary>
    /// 处理路由，构建菜单项
    /// </summary>
    /// <param name="allRouters">所有路由</param>
    /// <param name="routeNameJa">允许的路由名称</param>
    /// <param name="router">当前路由</param>
    /// <returns>菜单项</returns>
    private JObject? HandleRoute(List<Models.Entities.InternalRouter> allRouters, JArray routeNameJa, Models.Entities.InternalRouter router)
    {
        // 检查是否有权限访问此路由
        if (!routeNameJa.Any(r => r.ToString() == router.RouteName))
        {
            return null;
        }

        var menuItem = new JObject
        {
            ["name"] = router.RouteName,
            ["path"] = router.Path,
            ["component"] = router.Component,
            ["meta"] = new JObject
            {
                ["title"] = router.MetaTitle,
                ["icon"] = router.MetaIcon,
                ["order"] = router.MetaOrder,
                ["hideInMenu"] = router.MetaHideInMenu,
                ["hideInBreadcrumb"] = router.MetaHideInBreadcrumb,
                ["hideInTab"] = router.MetaHideInTab,
                ["hideChildrenInMenu"] = router.MetaHideChildrenInMenu,
                ["keepAlive"] = router.MetaKeepAlive,
                ["affixTab"] = router.MetaAffixTab,
                ["activeIcon"] = router.MetaActiveIcon,
                ["activePath"] = router.MetaActivePath,
                ["link"] = router.MetaLink
            }
        };

        if (!string.IsNullOrEmpty(router.Redirect))
        {
            menuItem["redirect"] = router.Redirect;
        }

        // 处理子路由
        var childRouters = allRouters
            .Where(r => r.ParentName == router.RouteName && !r.Del)
            .OrderBy(r => r.MetaOrder)
            .ToList();

        if (childRouters.Any())
        {
            var children = new JArray();
            foreach (var childRouter in childRouters)
            {
                var childMenuItem = HandleRoute(allRouters, routeNameJa, childRouter);
                if (childMenuItem != null)
                {
                    children.Add(childMenuItem);
                }
            }

            if (children.Any())
            {
                menuItem["children"] = children;
            }
        }

        return menuItem;
    }

    /// <summary>
    /// 从HTTP上下文中获取用户GUID
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <returns>用户GUID</returns>
    private string? GetUserGuidFromContext(HttpContext context)
    {
        if (!context.Request.Headers.ContainsKey("Authorization"))
            return null;

        var authHeader = context.Request.Headers["Authorization"].ToString();
        if (!authHeader.StartsWith("Bearer "))
            return null;

        var token = authHeader.Substring("Bearer ".Length).Trim();
        return _jwtService.GetUserGuidFromToken(token);
    }
}
