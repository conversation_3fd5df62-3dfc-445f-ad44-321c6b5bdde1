using Microsoft.AspNetCore.Mvc;
using WMSApiCore.Models.DTOs;
using WMSApiCore.Models.Configuration;

namespace WMSApiCore.Controllers;

/// <summary>
/// 健康检查控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly AppConfig _appConfig;
    private readonly ILogger<HealthController> _logger;

    public HealthController(AppConfig appConfig, ILogger<HealthController> logger)
    {
        _appConfig = appConfig;
        _logger = logger;
    }

    /// <summary>
    /// 健康检查
    /// </summary>
    /// <returns>系统状态</returns>
    [HttpGet]
    public ActionResult<ApiResponse<object>> Get()
    {
        var healthInfo = new
        {
            Status = "Healthy",
            Application = _appConfig.ApplicationName,
            Version = _appConfig.Version,
            Timestamp = DateTime.Now,
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production",
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId
        };

        return Ok(ApiResponse<object>.CreateSuccess(healthInfo, "系统运行正常"));
    }

    /// <summary>
    /// 获取系统信息
    /// </summary>
    /// <returns>系统信息</returns>
    [HttpGet("info")]
    public ActionResult<ApiResponse<object>> GetInfo()
    {
        var systemInfo = new
        {
            Application = new
            {
                Name = _appConfig.ApplicationName,
                Version = _appConfig.Version,
                DevelopmentMode = _appConfig.DevelopmentMode
            },
            Server = new
            {
                IP = _appConfig.WebServer.IP,
                Port = _appConfig.WebServer.Port,
                HttpsPort = _appConfig.WebServer.HttpsPort,
                EnableHttps = _appConfig.WebServer.EnableHttps
            },
            Runtime = new
            {
                Framework = Environment.Version.ToString(),
                OS = Environment.OSVersion.ToString(),
                MachineName = Environment.MachineName,
                ProcessorCount = Environment.ProcessorCount,
                WorkingSet = Environment.WorkingSet,
                StartTime = DateTime.Now.AddMilliseconds(-Environment.TickCount64)
            }
        };

        return Ok(ApiResponse<object>.CreateSuccess(systemInfo, "获取系统信息成功"));
    }
}
