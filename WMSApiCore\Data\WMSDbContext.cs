using Microsoft.EntityFrameworkCore;
using WMSApiCore.Models.Entities;

namespace WMSApiCore.Data;

/// <summary>
/// WMS数据库上下文
/// </summary>
public class WMSDbContext : DbContext
{
    public WMSDbContext(DbContextOptions<WMSDbContext> options) : base(options)
    {
    }

    // 内部配置表
    public DbSet<InternalAPI> InternalAPIs { get; set; }
    public DbSet<InternalApiCondition> InternalApiConditions { get; set; }
    public DbSet<InternalParameters> InternalParameters { get; set; }
    public DbSet<InternalPermission> InternalPermissions { get; set; }
    public DbSet<InternalRouter> InternalRouters { get; set; }

    // 系统用户和认证表
    public DbSet<SysUser> SysUsers { get; set; }
    public DbSet<SysRole> SysRoles { get; set; }
    public DbSet<SysAuthToken> SysAuthTokens { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置InternalAPI表
        modelBuilder.Entity<InternalAPI>(entity =>
        {
            entity.HasIndex(e => e.AccessPath).HasDatabaseName("IX_InternalAPI_AccessPath");
            entity.HasIndex(e => e.MappingFunction).HasDatabaseName("IX_InternalAPI_MappingFunction");
        });

        // 配置InternalParameters表
        modelBuilder.Entity<InternalParameters>(entity =>
        {
            entity.HasIndex(e => new { e.ParameterType, e.IsActived }).HasDatabaseName("IX_InternalParameters_Type_Active");
        });

        // 配置InternalRouter表
        modelBuilder.Entity<InternalRouter>(entity =>
        {
            entity.HasIndex(e => new { e.Level, e.Del }).HasDatabaseName("IX_InternalRouter_Level_Del");
            entity.HasIndex(e => e.ParentName).HasDatabaseName("IX_InternalRouter_ParentName");
        });

        // 配置SysUser表
        modelBuilder.Entity<SysUser>(entity =>
        {
            entity.HasIndex(e => e.UserID).IsUnique().HasDatabaseName("IX_SysUser_UserID");
            entity.HasIndex(e => new { e.Del, e.IsActived }).HasDatabaseName("IX_SysUser_Del_Active");
        });

        // 配置SysRole表
        modelBuilder.Entity<SysRole>(entity =>
        {
            entity.HasIndex(e => new { e.Del, e.IsActived }).HasDatabaseName("IX_SysRole_Del_Active");
        });

        // 配置SysAuthToken表
        modelBuilder.Entity<SysAuthToken>(entity =>
        {
            entity.HasIndex(e => new { e.UserGuid, e.IsAbandoned }).HasDatabaseName("IX_SysAuthToken_User_Abandoned");
        });
    }
}
