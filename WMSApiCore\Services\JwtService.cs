using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using WMSApiCore.Models.Configuration;

namespace WMSApiCore.Services;

/// <summary>
/// JWT服务
/// </summary>
public class JwtService
{
    private readonly JwtConfig _jwtConfig;
    private readonly ILogger<JwtService> _logger;
    private readonly JwtSecurityTokenHandler _tokenHandler;
    private readonly SigningCredentials _signingCredentials;

    public JwtService(JwtConfig jwtConfig, ILogger<JwtService> logger)
    {
        _jwtConfig = jwtConfig;
        _logger = logger;
        _tokenHandler = new JwtSecurityTokenHandler();
        
        var key = Encoding.UTF8.GetBytes(_jwtConfig.SecretKey);
        _signingCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256);
    }

    /// <summary>
    /// 生成访问令牌
    /// </summary>
    /// <param name="userGuid">用户GUID</param>
    /// <param name="userId">用户ID</param>
    /// <param name="userName">用户名</param>
    /// <param name="expiryMinutes">过期时间（分钟）</param>
    /// <returns>JWT令牌</returns>
    public string GenerateAccessToken(string userGuid, string userId, string userName, int? expiryMinutes = null)
    {
        var expiry = expiryMinutes ?? _jwtConfig.AccessTokenExpiryMinutes;
        
        var claims = new[]
        {
            new Claim("sub", userGuid),
            new Claim("id", userId),
            new Claim("name", userName),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(expiry),
            Issuer = _jwtConfig.Issuer,
            Audience = _jwtConfig.Audience,
            SigningCredentials = _signingCredentials
        };

        var token = _tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = _tokenHandler.WriteToken(token);

        _logger.LogDebug("生成访问令牌成功，用户: {UserId}, 过期时间: {Expiry}分钟", userId, expiry);
        
        return tokenString;
    }

    /// <summary>
    /// 生成刷新令牌
    /// </summary>
    /// <param name="userGuid">用户GUID</param>
    /// <param name="userId">用户ID</param>
    /// <param name="userName">用户名</param>
    /// <param name="expiryMinutes">过期时间（分钟）</param>
    /// <returns>JWT令牌</returns>
    public string GenerateRefreshToken(string userGuid, string userId, string userName, int? expiryMinutes = null)
    {
        var expiry = expiryMinutes ?? _jwtConfig.RefreshTokenExpiryMinutes;
        
        var claims = new[]
        {
            new Claim("sub", userGuid),
            new Claim("id", userId),
            new Claim("name", userName),
            new Claim("type", "refresh"),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(expiry),
            Issuer = _jwtConfig.Issuer,
            Audience = _jwtConfig.Audience,
            SigningCredentials = _signingCredentials
        };

        var token = _tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = _tokenHandler.WriteToken(token);

        _logger.LogDebug("生成刷新令牌成功，用户: {UserId}, 过期时间: {Expiry}分钟", userId, expiry);
        
        return tokenString;
    }

    /// <summary>
    /// 验证令牌
    /// </summary>
    /// <param name="token">JWT令牌</param>
    /// <returns>声明主体</returns>
    public async Task<ClaimsPrincipal?> ValidateTokenAsync(string token)
    {
        try
        {
            var key = Encoding.UTF8.GetBytes(_jwtConfig.SecretKey);
            
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _jwtConfig.Issuer,
                ValidateAudience = true,
                ValidAudience = _jwtConfig.Audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var result = await _tokenHandler.ValidateTokenAsync(token, validationParameters);
            
            if (result.IsValid)
            {
                return new ClaimsPrincipal(result.ClaimsIdentity);
            }
            
            _logger.LogWarning("令牌验证失败: {Error}", result.Exception?.Message);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证令牌时发生错误");
            return null;
        }
    }

    /// <summary>
    /// 解码令牌（不验证）
    /// </summary>
    /// <param name="token">JWT令牌</param>
    /// <returns>声明字典</returns>
    public Dictionary<string, string> DecodeToken(string token)
    {
        try
        {
            var jsonToken = _tokenHandler.ReadJwtToken(token);
            var claims = new Dictionary<string, string>();

            foreach (var claim in jsonToken.Claims)
            {
                claims[claim.Type] = claim.Value;
            }

            return claims;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解码令牌时发生错误");
            return new Dictionary<string, string>();
        }
    }

    /// <summary>
    /// 检查令牌是否过期
    /// </summary>
    /// <param name="token">JWT令牌</param>
    /// <returns>是否过期</returns>
    public bool IsTokenExpired(string token)
    {
        try
        {
            var jsonToken = _tokenHandler.ReadJwtToken(token);
            return jsonToken.ValidTo < DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查令牌过期时发生错误");
            return true;
        }
    }

    /// <summary>
    /// 从令牌中获取用户GUID
    /// </summary>
    /// <param name="token">JWT令牌</param>
    /// <returns>用户GUID</returns>
    public string? GetUserGuidFromToken(string token)
    {
        var claims = DecodeToken(token);
        claims.TryGetValue("sub", out var userGuid);
        return userGuid;
    }

    /// <summary>
    /// 从令牌中获取用户ID
    /// </summary>
    /// <param name="token">JWT令牌</param>
    /// <returns>用户ID</returns>
    public string? GetUserIdFromToken(string token)
    {
        var claims = DecodeToken(token);
        claims.TryGetValue("id", out var userId);
        return userId;
    }
}
