using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMSApiCore.Models.Entities;

/// <summary>
/// 内部参数表
/// </summary>
[Table("InternalParameters")]
public class InternalParameters
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 参数类型
    /// </summary>
    [StringLength(24)]
    public string ParameterType { get; set; } = string.Empty;

    /// <summary>
    /// 参数名称
    /// </summary>
    [StringLength(48)]
    public string ParameterName { get; set; } = string.Empty;

    /// <summary>
    /// 参数值
    /// </summary>
    [StringLength(64)]
    public string ParameterValue { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    [StringLength(64)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 参数扩展值
    /// </summary>
    [StringLength(16)]
    public string ParameterValueExt { get; set; } = string.Empty;

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActived { get; set; } = true;

    /// <summary>
    /// 标签颜色
    /// </summary>
    [StringLength(24)]
    public string TagColor { get; set; } = string.Empty;

    /// <summary>
    /// 是否默认
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// 排序键
    /// </summary>
    public int SortKey { get; set; } = 0;
}
