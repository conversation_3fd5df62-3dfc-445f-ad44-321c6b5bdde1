namespace WMSApiCore.Models.Configuration;

/// <summary>
/// JWT配置类
/// </summary>
public class JwtConfig
{
    /// <summary>
    /// JWT密钥
    /// </summary>
    public string SecretKey { get; set; } = "Tpit#123@yxw_WMS_JWT_SECRET_KEY_2024";

    /// <summary>
    /// 发行者
    /// </summary>
    public string Issuer { get; set; } = "WMSApiCore";

    /// <summary>
    /// 受众
    /// </summary>
    public string Audience { get; set; } = "WMSApiCore";

    /// <summary>
    /// AccessToken过期时间（分钟）
    /// </summary>
    public int AccessTokenExpiryMinutes { get; set; } = 120;

    /// <summary>
    /// RefreshToken过期时间（分钟）
    /// </summary>
    public int RefreshTokenExpiryMinutes { get; set; } = 2880; // 48小时
}
