{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "AppConfig": {"ApplicationName": "WMS API Core", "Version": "1.0.0", "DevelopmentMode": false, "Database": {"SQLName": "TPIT_WMS", "LogSQLName": "TPIT_WMS_LOG", "SourceIP_Internal": "127.0.0.1", "SourcePort_Internal": "1433", "SourceIP_External": "127.0.0.1", "SourcePort_External": "1433", "Username": "tpit", "Password": "Tpit#123", "LogSourceIP_Internal": "127.0.0.1", "LogSourcePort_Internal": "1433", "LogSourceIP_External": "127.0.0.1", "LogSourcePort_External": "1433"}, "WebServer": {"IP": "0.0.0.0", "Port": 5000, "HttpsPort": 5001, "EnableHttps": false}, "Jwt": {"SecretKey": "Tpit#123@yxw_WMS_JWT_SECRET_KEY_2024", "Issuer": "WMSApiCore", "Audience": "WMSApiCore", "AccessTokenExpiryMinutes": 120, "RefreshTokenExpiryMinutes": 2880}}}