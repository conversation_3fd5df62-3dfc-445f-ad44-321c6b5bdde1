using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMSApiCore.Models.Entities;

/// <summary>
/// 系统角色表
/// </summary>
[Table("SysRole")]
public class SysRole
{
    /// <summary>
    /// 主键GUID
    /// </summary>
    [Key]
    [StringLength(36)]
    public string Guid { get; set; } = string.Empty;

    /// <summary>
    /// 角色名称
    /// </summary>
    [StringLength(64)]
    public string RoleName { get; set; } = string.Empty;

    /// <summary>
    /// 角色描述
    /// </summary>
    [StringLength(255)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 权限（JSON格式）
    /// </summary>
    [Column(TypeName = "nvarchar(max)")]
    public string Permission { get; set; } = "[]";

    /// <summary>
    /// 路由（JSON格式）
    /// </summary>
    [Column(TypeName = "nvarchar(max)")]
    public string Route { get; set; } = "[]";

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActived { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateDate { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否删除
    /// </summary>
    public bool Del { get; set; } = false;
}
