using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMSApiCore.Models.Entities;

/// <summary>
/// 系统认证令牌表
/// </summary>
[Table("SysAuthToken")]
public class SysAuthToken
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 用户GUID
    /// </summary>
    [StringLength(36)]
    public string UserGuid { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    [StringLength(32)]
    public string UserID { get; set; } = string.Empty;

    /// <summary>
    /// 刷新令牌
    /// </summary>
    [Column(TypeName = "nvarchar(max)")]
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateDate { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后刷新时间
    /// </summary>
    public DateTime LastRefreshDate { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否已废弃
    /// </summary>
    public bool IsAbandoned { get; set; } = false;
}
