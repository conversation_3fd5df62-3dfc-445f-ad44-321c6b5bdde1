using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using WMSApiCore.Data;
using WMSApiCore.Models.Configuration;
using WMSApiCore.Services;
using WMSApiCore.Services.Business;
using WMSApiCore.Middleware;

var builder = WebApplication.CreateBuilder(args);

// 读取配置
var appConfig = builder.Configuration.GetSection("AppConfig").Get<AppConfig>() ?? new AppConfig();

// 配置监听地址和端口
builder.WebHost.ConfigureKestrel(options =>
{
    options.ListenAnyIP(appConfig.WebServer.Port);
    if (appConfig.WebServer.EnableHttps)
    {
        options.ListenAnyIP(appConfig.WebServer.HttpsPort, listenOptions =>
        {
            listenOptions.UseHttps();
        });
    }
});

// 添加服务到容器
builder.Services.AddControllers();
builder.Services.AddOpenApi();
builder.Services.AddMemoryCache();

// 注册配置
builder.Services.AddSingleton(appConfig);
builder.Services.AddSingleton(appConfig.Database);
builder.Services.AddSingleton(appConfig.WebServer);
builder.Services.AddSingleton(appConfig.Jwt);

// 配置数据库连接
builder.Services.AddScoped<DatabaseConnectionService>();
builder.Services.AddDbContext<WMSDbContext>((serviceProvider, options) =>
{
    var connectionService = serviceProvider.GetRequiredService<DatabaseConnectionService>();
    var connectionString = connectionService.GetConnectionStringAsync("sys").Result;
    options.UseSqlServer(connectionString);
});

// 配置JWT认证
var key = Encoding.UTF8.GetBytes(appConfig.Jwt.SecretKey);
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(key),
            ValidateIssuer = true,
            ValidIssuer = appConfig.Jwt.Issuer,
            ValidateAudience = true,
            ValidAudience = appConfig.Jwt.Audience,
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };
    });

builder.Services.AddAuthorization();

// 注册服务
builder.Services.AddScoped<JwtService>();
builder.Services.AddScoped<EncryptionService>();
builder.Services.AddScoped<MemoryCacheService>();
builder.Services.AddScoped<FunctionExecutionService>();

// 注册业务服务
builder.Services.AddScoped<AuthService>();
builder.Services.AddScoped<MenuService>();
builder.Services.AddScoped<CommonService>();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseDeveloperExceptionPage();
}

app.UseCors();
app.UseAuthentication();
app.UseAuthorization();

// 使用动态API中间件
app.UseMiddleware<DynamicApiMiddleware>();

app.MapControllers();

// 启动信息
var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("WMS API Core 启动成功");
logger.LogInformation("监听地址: http://{IP}:{Port}", appConfig.WebServer.IP, appConfig.WebServer.Port);
if (appConfig.WebServer.EnableHttps)
{
    logger.LogInformation("HTTPS监听地址: https://{IP}:{Port}", appConfig.WebServer.IP, appConfig.WebServer.HttpsPort);
}

app.Run();
