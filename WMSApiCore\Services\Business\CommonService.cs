using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using WMSApiCore.Data;
using WMSApiCore.Models.DTOs;

namespace WMSApiCore.Services.Business;

/// <summary>
/// 通用业务服务
/// </summary>
public class CommonService
{
    private readonly WMSDbContext _dbContext;
    private readonly MemoryCacheService _cacheService;
    private readonly ILogger<CommonService> _logger;

    public CommonService(
        WMSDbContext dbContext,
        MemoryCacheService cacheService,
        ILogger<CommonService> logger)
    {
        _dbContext = dbContext;
        _cacheService = cacheService;
        _logger = logger;
    }

    /// <summary>
    /// API_CommonGetOptions - 获取通用选项数据
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="parameters">参数</param>
    /// <returns>选项数据</returns>
    public async Task<ApiResponse<JArray>> API_CommonGetOptions(HttpContext context, Dictionary<string, object> parameters)
    {
        try
        {
            if (!parameters.TryGetValue("type", out var typeObj))
            {
                return ApiResponse<JArray>.CreateError("类型参数不能为空");
            }

            var type = typeObj.ToString()!;
            var result = new JArray();

            switch (type)
            {
                case "DictionaryType":
                    result = await GetDictionaryTypeOptions();
                    break;

                case "EventListOptions":
                    result = await GetEventListOptions();
                    break;

                case "StockDetail":
                    result = await GetStockDetailOptions();
                    break;

                case "StockOverView":
                    result = await GetStockOverViewOptions();
                    break;

                default:
                    if (type.StartsWith("StockTaking_"))
                    {
                        var warehouseCode = type.Replace("StockTaking_", "");
                        result = await GetStockTakingOptions(warehouseCode);
                    }
                    break;
            }

            return ApiResponse<JArray>.CreateSuccess(result, "获取选项数据成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取选项数据时发生错误");
            return ApiResponse<JArray>.CreateError("获取选项数据失败");
        }
    }

    /// <summary>
    /// 获取字典类型选项
    /// </summary>
    /// <returns>选项数组</returns>
    private async Task<JArray> GetDictionaryTypeOptions()
    {
        var ja = new JArray();
        var parameters = await _cacheService.GetParametersByTypeAsync("DictionaryType");

        foreach (var param in parameters.Where(p => p.IsActived).OrderBy(p => p.SortKey))
        {
            var jo = new JObject
            {
                ["label"] = param.ParameterName,
                ["value"] = param.ParameterValue
            };
            ja.Add(jo);
        }

        return ja;
    }

    /// <summary>
    /// 获取事件列表选项
    /// </summary>
    /// <returns>选项数组</returns>
    private async Task<JArray> GetEventListOptions()
    {
        var ja = new JArray();

        // TODO: 实现Kafka主题相关逻辑
        // 这里需要根据实际的Kafka配置表来实现

        // 添加默认选项
        var needlessJo = new JObject
        {
            ["label"] = "Needless",
            ["value"] = -1,
            ["type"] = "CallStatus"
        };
        ja.Add(needlessJo);

        var pendingJo = new JObject
        {
            ["label"] = "Pending",
            ["value"] = 0,
            ["type"] = "CallStatus"
        };
        ja.Add(pendingJo);

        return ja;
    }

    /// <summary>
    /// 获取库存明细选项
    /// </summary>
    /// <returns>选项数组</returns>
    private async Task<JArray> GetStockDetailOptions()
    {
        var ja = new JArray();

        // TODO: 实现基础数据表查询
        // 这里需要根据实际的基础数据表来实现
        // 例如：BaseBrand, BaseOwner, BaseWarehouse, BaseDictionary等

        // 从内部参数获取相关选项
        var parameterTypes = new[] { "StockStatus", "Storagelocation", "MaterialType", "Division" };
        
        foreach (var paramType in parameterTypes)
        {
            var parameters = await _cacheService.GetParametersByTypeAsync(paramType);
            foreach (var param in parameters.Where(p => p.IsActived).OrderBy(p => p.SortKey))
            {
                var jo = new JObject
                {
                    ["label"] = param.Description,
                    ["value"] = param.ParameterValue,
                    ["type"] = param.ParameterType
                };
                ja.Add(jo);
            }
        }

        return ja;
    }

    /// <summary>
    /// 获取库存概览选项
    /// </summary>
    /// <returns>选项数组</returns>
    private async Task<JArray> GetStockOverViewOptions()
    {
        var ja = new JArray();

        // TODO: 实现基础数据表查询
        // 这里需要根据实际的BaseOwner表来实现

        // 从内部参数获取相关选项
        var parameterTypes = new[] { "Storagelocation", "MaterialType" };
        
        foreach (var paramType in parameterTypes)
        {
            var parameters = await _cacheService.GetParametersByTypeAsync(paramType);
            foreach (var param in parameters.Where(p => p.IsActived).OrderBy(p => p.SortKey))
            {
                var jo = new JObject
                {
                    ["label"] = param.Description,
                    ["value"] = param.ParameterValue,
                    ["type"] = param.ParameterType
                };
                ja.Add(jo);
            }
        }

        return ja;
    }

    /// <summary>
    /// 获取盘点选项
    /// </summary>
    /// <param name="warehouseCode">仓库代码</param>
    /// <returns>选项数组</returns>
    private async Task<JArray> GetStockTakingOptions(string warehouseCode)
    {
        var ja = new JArray();

        // TODO: 实现基础数据表查询
        // 这里需要根据实际的BaseBrand, BaseArea等表来实现

        // 从内部参数获取相关选项
        var parameterTypes = new[] { "StockStatus", "Storagelocation", "MaterialType", "Division" };
        
        foreach (var paramType in parameterTypes)
        {
            var parameters = await _cacheService.GetParametersByTypeAsync(paramType);
            foreach (var param in parameters.Where(p => p.IsActived).OrderBy(p => p.SortKey))
            {
                var jo = new JObject
                {
                    ["label"] = param.Description,
                    ["value"] = param.ParameterValue,
                    ["type"] = param.ParameterType
                };
                ja.Add(jo);
            }
        }

        return ja;
    }

    /// <summary>
    /// 将DataTable转换为JSON
    /// </summary>
    /// <param name="dataTable">数据表</param>
    /// <param name="includeSchema">是否包含架构</param>
    /// <returns>JSON数组</returns>
    private JArray DataTableToJson(System.Data.DataTable dataTable, bool includeSchema = false)
    {
        var ja = new JArray();

        foreach (System.Data.DataRow row in dataTable.Rows)
        {
            var jo = new JObject();
            foreach (System.Data.DataColumn column in dataTable.Columns)
            {
                jo[column.ColumnName] = JToken.FromObject(row[column] ?? DBNull.Value);
            }
            ja.Add(jo);
        }

        return ja;
    }
}
