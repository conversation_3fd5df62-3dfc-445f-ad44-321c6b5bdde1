using System.Reflection;
using WMSApiCore.Models.DTOs;

namespace WMSApiCore.Services;

/// <summary>
/// 函数执行服务
/// </summary>
public class FunctionExecutionService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<FunctionExecutionService> _logger;
    private readonly Dictionary<string, MethodInfo> _functionCache;

    public FunctionExecutionService(IServiceProvider serviceProvider, ILogger<FunctionExecutionService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _functionCache = new Dictionary<string, MethodInfo>();
        
        // 初始化函数缓存
        InitializeFunctionCache();
    }

    /// <summary>
    /// 执行函数
    /// </summary>
    /// <param name="functionName">函数名称</param>
    /// <param name="context">HTTP上下文</param>
    /// <param name="parameters">参数</param>
    /// <returns>执行结果</returns>
    public async Task<object> ExecuteAsync(string functionName, HttpContext context, Dictionary<string, object> parameters)
    {
        try
        {
            _logger.LogInformation("执行函数: {FunctionName}", functionName);

            // 查找函数
            if (!_functionCache.TryGetValue(functionName, out var method))
            {
                _logger.LogWarning("未找到函数: {FunctionName}", functionName);
                return ApiResponse<object>.CreateError($"未找到函数: {functionName}");
            }

            // 获取服务实例
            var serviceType = method.DeclaringType!;
            var service = _serviceProvider.GetService(serviceType);
            
            if (service == null)
            {
                _logger.LogError("无法获取服务实例: {ServiceType}", serviceType.Name);
                return ApiResponse<object>.CreateError($"无法获取服务实例: {serviceType.Name}");
            }

            // 准备参数
            var methodParameters = PrepareMethodParameters(method, context, parameters);

            // 执行方法
            var result = method.Invoke(service, methodParameters);

            // 处理异步结果
            if (result is Task task)
            {
                await task;
                
                if (task.GetType().IsGenericType)
                {
                    var property = task.GetType().GetProperty("Result");
                    result = property?.GetValue(task);
                }
                else
                {
                    result = null;
                }
            }

            return result ?? ApiResponse<object>.CreateSuccess(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行函数 {FunctionName} 时发生错误", functionName);
            return ApiResponse<object>.CreateError(ex.Message);
        }
    }

    /// <summary>
    /// 初始化函数缓存
    /// </summary>
    private void InitializeFunctionCache()
    {
        try
        {
            // 扫描所有业务服务类
            var assembly = Assembly.GetExecutingAssembly();
            var serviceTypes = assembly.GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && t.Namespace?.Contains("Services.Business") == true);

            foreach (var serviceType in serviceTypes)
            {
                var methods = serviceType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                    .Where(m => m.IsPublic && !m.IsSpecialName);

                foreach (var method in methods)
                {
                    // 构建函数名称（类名\\方法名）
                    var functionName = $"{serviceType.Name.Replace("Service", "")}\\{method.Name}";
                    _functionCache[functionName] = method;
                    
                    _logger.LogDebug("注册函数: {FunctionName}", functionName);
                }
            }

            _logger.LogInformation("已注册 {Count} 个函数到缓存", _functionCache.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化函数缓存时发生错误");
        }
    }

    /// <summary>
    /// 准备方法参数
    /// </summary>
    /// <param name="method">方法信息</param>
    /// <param name="context">HTTP上下文</param>
    /// <param name="parameters">参数字典</param>
    /// <returns>方法参数数组</returns>
    private object[] PrepareMethodParameters(MethodInfo method, HttpContext context, Dictionary<string, object> parameters)
    {
        var parameterInfos = method.GetParameters();
        var methodParameters = new object[parameterInfos.Length];

        for (int i = 0; i < parameterInfos.Length; i++)
        {
            var paramInfo = parameterInfos[i];
            var paramType = paramInfo.ParameterType;

            // HttpContext参数
            if (paramType == typeof(HttpContext))
            {
                methodParameters[i] = context;
            }
            // 参数字典
            else if (paramType == typeof(Dictionary<string, object>))
            {
                methodParameters[i] = parameters;
            }
            // 字符串参数
            else if (paramType == typeof(string))
            {
                parameters.TryGetValue(paramInfo.Name!, out var value);
                methodParameters[i] = value?.ToString() ?? string.Empty;
            }
            // 其他类型参数
            else
            {
                if (parameters.TryGetValue(paramInfo.Name!, out var value))
                {
                    try
                    {
                        methodParameters[i] = Convert.ChangeType(value, paramType);
                    }
                    catch
                    {
                        methodParameters[i] = paramType.IsValueType ? Activator.CreateInstance(paramType) : null;
                    }
                }
                else
                {
                    methodParameters[i] = paramType.IsValueType ? Activator.CreateInstance(paramType) : null;
                }
            }
        }

        return methodParameters;
    }

    /// <summary>
    /// 获取所有已注册的函数
    /// </summary>
    /// <returns>函数名称列表</returns>
    public List<string> GetRegisteredFunctions()
    {
        return _functionCache.Keys.ToList();
    }
}
