{\rtf1\ansi\ansicpg936\deff0\deflang1033\deflangfe2052{\fonttbl{\f0\fnil\fcharset134 \'cb\'ce\'cc\'e5;}}
{\colortbl ;\red255\green0\blue0;\red0\green0\blue255;\red0\green0\blue0;}
\viewkind4\uc1\pard\cf1\lang2052\f0\fs18\'cf\'ee\'c4\'bf\'ca\'c2\'bc\'fe\par
\par
\cf2 BeforeConnectOuterDataSource\par
\par
\cf3 Select Case e.Name\par
    Case "sys"\par
        '\'b6\'c1\'c8\'a1\'c5\'e4\'d6\'c3\'ce\'c4\'bc\'fe\par
        Dim filePath As String = e.ProjectPath & "sqlcfg.json"\par
        If Filesys.FileExists(filePath) = False Then\par
            MessageBox.Show("\'c5\'e4\'d6\'c3\'ce\'c4\'bc\'fe\'b2\'bb\'b4\'e6\'d4\'da\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'a3\'a1")\par
            Return\par
        End If \par
        Dim jsonStr As String = FileSys.ReadAllText(filePath)\par
        \par
        '\'bd\'ab\'c5\'e4\'d6\'c3\'c4\'da\'c8\'dd\'d7\'aa\'ce\'aaJObject\'a3\'ac\'b6\'c1\'c8\'a1\'ca\'fd\'be\'dd\'bf\'e2\'c5\'e4\'d6\'c3\'b2\'ce\'ca\'fd\par
        Dim jo As New JObject\par
        Dim sqlName, sourceInternalIP, sourceInternalPort, sourceExternalIP, sourceExternalPort As String \par
        Try\par
            jo = JObject.Parse(jsonStr)\par
            sqlName = jo("SQLName").ToString\par
            sourceInternalIP = jo("SourceIP_Internal").ToString\par
            sourceInternalPort = jo("SourcePort_Internal").ToString\par
            sourceExternalIP = jo("SourceIP_External").ToString\par
            sourceExternalPort = jo("SourcePort_External").ToString\par
        Catch ex As Exception \par
            MessageBox.Show("\'c5\'e4\'d6\'c3\'ce\'c4\'bc\'fe\'c4\'da\'c8\'dd\'b4\'ed\'ce\'f3\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'a3\'a1")\par
            Return\par
        End Try\par
        '\'ca\'b9\'d3\'c3\'c5\'e4\'d6\'c3\'b2\'ce\'ca\'fd\'d0\'de\'b8\'c4\'ca\'fd\'be\'dd\'d4\'b4\'c1\'ac\'bd\'d3\'d7\'d6\'b7\'fb\'b4\'ae\par
        e.ConnectionString = e.ConnectionString.Replace("TPIT_WMS", sqlName)\par
        If Network.Ping(sourceInternalIP, 1000) Then\par
            '\'ca\'d7\'d1\'a1\'c4\'da\'cd\'f8\'b5\'d8\'d6\'b7\par
            e.ConnectionString = e.ConnectionString.Replace("127.0.0.1", sourceInternalIP & "," & sourceInternalPort)\par
        ElseIf Network.Ping(sourceExternalIP, 1000) Then\par
            '\'b4\'ce\'d1\'a1\'cd\'e2\'cd\'f8\'b5\'d8\'d6\'b7\par
            e.ConnectionString = e.ConnectionString.Replace("127.0.0.1", sourceExternalIP & "," & sourceExternalPort)\par
        Else\par
            '\'c4\'da\'cd\'e2\'cd\'f8\'be\'f9\'ce\'de\'b7\'a8Ping\'cd\'a8\'a3\'ac\'c5\'d7\'b3\'f6\'cc\'e1\'ca\'be\par
            MessageBox.Show("\'ca\'fd\'be\'dd\'d4\'b4\'c1\'ac\'bd\'d3\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'cd\'f8\'c2\'e7\'bc\'b0\'b2\'ce\'ca\'fd\'c5\'e4\'d6\'c3\'a3\'a1")\par
            Return\par
        End If\par
    Case "log"\par
        \par
        '\'b6\'c1\'c8\'a1\'c5\'e4\'d6\'c3\'ce\'c4\'bc\'fe\par
        Dim filePath As String = e.ProjectPath & "sqlcfg.json"\par
        If Filesys.FileExists(filePath) = False Then\par
            MessageBox.Show("\'c5\'e4\'d6\'c3\'ce\'c4\'bc\'fe\'b2\'bb\'b4\'e6\'d4\'da\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'a3\'a1")\par
            Return\par
        End If \par
        Dim jsonStr As String = FileSys.ReadAllText(filePath)\par
        \par
        '\'bd\'ab\'c5\'e4\'d6\'c3\'c4\'da\'c8\'dd\'d7\'aa\'ce\'aaJObject\'a3\'ac\'b6\'c1\'c8\'a1\'ca\'fd\'be\'dd\'bf\'e2\'c5\'e4\'d6\'c3\'b2\'ce\'ca\'fd\par
        Dim jo As New JObject\par
        Dim logSqlName, logSourceInternalIP, logSourceInternalPort, logSourceExternalIP, logSourceExternalPort As String \par
        Try\par
            jo = JObject.Parse(jsonStr)\par
            logSqlName = jo("LogSQLName").ToString\par
            logSourceInternalIP = jo("LogSourceIP_Internal").ToString\par
            logSourceInternalPort = jo("LogSourcePort_Internal").ToString\par
            logSourceExternalIP = jo("LogSourceIP_External").ToString\par
            logSourceExternalPort = jo("LogSourcePort_External").ToString\par
        Catch ex As Exception \par
            MessageBox.Show("\'c5\'e4\'d6\'c3\'ce\'c4\'bc\'fe\'c4\'da\'c8\'dd\'b4\'ed\'ce\'f3\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'a3\'a1")\par
            Return\par
        End Try\par
        '\'ca\'b9\'d3\'c3\'c5\'e4\'d6\'c3\'b2\'ce\'ca\'fd\'d0\'de\'b8\'c4\'c8\'d5\'d6\'be\'ca\'fd\'be\'dd\'d4\'b4\'c1\'ac\'bd\'d3\'d7\'d6\'b7\'fb\'b4\'ae\par
        e.ConnectionString = e.ConnectionString.Replace("TPIT_WMS_LOG", logSqlName)\par
        If Network.Ping(logSourceInternalIP, 1000) Then\par
            '\'ca\'d7\'d1\'a1\'c4\'da\'cd\'f8\'b5\'d8\'d6\'b7\par
            e.ConnectionString = e.ConnectionString.Replace("127.0.0.1", logSourceInternalIP & "," & logSourceInternalPort)\par
        ElseIf Network.Ping(logSourceExternalIP, 1000) Then\par
            '\'b4\'ce\'d1\'a1\'cd\'e2\'cd\'f8\'b5\'d8\'d6\'b7\par
            e.ConnectionString = e.ConnectionString.Replace("127.0.0.1", logSourceExternalIP & "," & logSourceExternalPort)\par
        Else\par
            '\'c4\'da\'cd\'e2\'cd\'f8\'be\'f9\'ce\'de\'b7\'a8Ping\'cd\'a8\'a3\'ac\'c5\'d7\'b3\'f6\'cc\'e1\'ca\'be\par
            MessageBox.Show("\'ca\'fd\'be\'dd\'d4\'b4\'c1\'ac\'bd\'d3\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'cd\'f8\'c2\'e7\'bc\'b0\'b2\'ce\'ca\'fd\'c5\'e4\'d6\'c3\'a3\'a1")\par
            Return\par
        End If\par
End Select\par
\par
\cf2 AfterOpenProject\par
\par
\cf3 Functions.Execute("System_AfterOpenProject")\par
\par
\cf2 BeforeCloseProject\par
\par
\cf3 e.SkipSave = True\par
\par
\cf2 HttpRequest\par
\par
\cf3 Functions.Execute("WebService_Main", e)\par
\par
\cf2 BeforeHttpRequest\par
\par
\cf3 Functions.Execute("WebService_BeforeRequest", e)\par
\par
\cf1\'bc\'c6\'bb\'ae\'b9\'dc\'c0\'ed\par
\par
\'b1\'ed\'ca\'c2\'bc\'fe\par
\par
\cf2 InternalParameters_PrepareEdit\par
\par
\cf3 'e.Cancel = True\par
\par
\cf2 InternalParameters_AfterSaveDataRow\par
\par
\cf3 ' \'b8\'fc\'d0\'c2\'c8\'ab\'be\'d6\'b1\'e4\'c1\'bf _sysParm\par
Functions.Execute("System_UpdateSysParameters")\par
\par
'\'c8\'f4\'d0\'de\'b8\'c4\'c1\'cbSQLServer\'cf\'e0\'b9\'d8\'b2\'ce\'ca\'fd\'a3\'ac\'b8\'fc\'d0\'c2sqlcfg.json\'ce\'c4\'bc\'fe\'a3\'ac\'d2\'d4\'b1\'e3\'cf\'ee\'c4\'bf\'b4\'f2\'bf\'aa\'ca\'b1\'cc\'e6\'bb\'bb\'ca\'fd\'be\'dd\'d4\'b4\'d7\'d6\'b7\'fb\par
If e.DataRow("ParameterType") = "SQLServer" Then\par
    Dim filePath As String = ProjectPath & "sqlcfg.json"\par
    FileSys.WriteAllText(filePath, _sysParms("SQLServer").ToString(), False, New UTF8Encoding(False))\par
End If\par
\par
\cf2 InternalParameters_DoubleClick\par
\par
\cf3 'Forms("ParametersModal").Show()\par
\par
\cf2 InternalRouter_DataRowAdding\par
\par
\cf3 e.DataRow("Guid")  = Guid.NewGuid.ToString()\par
\par
\cf1\'b4\'b0\'bf\'da\'b1\'ed\'ca\'c2\'bc\'fe\par
\par
\'b4\'b0\'bf\'da\'d3\'eb\'bf\'d8\'bc\'fe\'ca\'c2\'bc\'fe\par
\par
\cf2 Clear_Button1_Click\par
\par
\cf3 Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
cmd.CommandText = "Delete From BWInOrderHead Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From BWInOrderItem Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From BWOutOrderHead Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From BWOutOrderItem Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From BWOutProcessing Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From BWOutTally Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From BWOutPickingHead Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From BWOutPickingItem Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From BWOutDispatchHead Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From BWOutDispatchItem Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From Record4Pallet Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From StockDetail Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From StockFlow Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From WMSStockMovementHead Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
cmd.CommandText = "Delete From WMSStockMovementItem Where 1=1"\par
cmd.ExecuteNonQuery\par
\par
MessageBox.Show("Finished")\par
\par
\cf2 MainFrm_AfterLoad\par
\par
\cf3 '\'cb\'e6\'b4\'b0\'bf\'da\'bc\'d3\'d4\'d8\'a3\'ac\'c6\'f4\'b6\'afWeb\'b7\'fe\'ce\'f1\par
If HttpServer.IsRunning = False Then\par
    e.Form.Controls("ProgressBar1").Value = 0\par
    e.Form.Controls("Button_Start").PerformClick\par
Else\par
    e.Form.Controls("ProgressBar1").Value = e.Form.Controls("ProgressBar1").Maximum\par
    e.Form.Controls("Button_Start").Enabled = False\par
    e.Form.Controls("Button_Stop").Enabled = True\par
End If\par
\par
\cf2 MainFrm_Button_Start_Click\par
\par
\cf3 '\'c6\'f4\'b6\'afWeb\'b7\'fe\'ce\'f1\par
If HttpServer.IsRunning = False Then\par
    HttpServer.Close\par
    '\'bb\'f1\'c8\'a1Web\'b7\'fe\'ce\'f1\'b2\'ce\'ca\'fd\par
    Dim ip, port As String\par
    Try\par
        ip = _sysParms("WebServer")("IP").ToString\par
        port = _sysParms("WebServer")("Port").ToString \par
    Catch ex As Exception \par
        MessageBox.Show("Web\'b2\'ce\'ca\'fd\'bb\'f1\'c8\'a1\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'c5\'e4\'d6\'c3\'a3\'a1")\par
        Return\par
    End Try\par
    \par
    '\'d5\'fd\'d4\'f2\'d0\'a3\'d1\'e9IP\'ba\'cd\'b6\'cb\'bf\'da\par
    Dim ip_port As String = ip & ":" & port\par
    'Dim pattern As String = "^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\s*:\\s*(6553[0-5]|655[0-9]\{2\}|65[0-4][0-9]\{2\}|6[0-4][0-9]\{3\}|[1-5][0-9]\{4\}|[1-9][0-9]\{0,3\}|0)$"\par
    'Dim regex As New Regex(pattern)\par
\par
    'If regex.IsMatch(ip_port) = False Then\par
        'MessageBox.Show("Web\'b7\'fe\'ce\'f1\'b2\'ce\'ca\'fd\'b4\'ed\'ce\'f3\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'c5\'e4\'d6\'c3\'a3\'a1")\par
        'Return\par
    'End If \par
    Output.Show("http://" & ip_port & "/")\par
    '\'c6\'f4\'b6\'afHttpServer\par
    e.Form.Controls("Button_Start").Enabled = False\par
    HttpServer.Prefixes.Add("http://" & ip_port & "/")\par
    HttpServer.WebPath = ProjectPath & "Web\\"\par
    HttpServer.Start() \par
\par
    '\'d2\'c0\'be\'ddHttpServer\'d4\'cb\'d0\'d0\'d7\'b4\'cc\'ac\'a3\'ac\'b4\'a6\'c0\'ed\'bf\'d8\'bc\'fe\'ca\'f4\'d0\'d4\par
    If HttpServer.IsRunning Then\par
        e.Form.Controls("Button_Stop").Enabled = True\par
        e.Form.Controls("ProgressBar1").Value = 100\par
    Else\par
        e.Form.Controls("Button_Start").Enabled = True\par
        e.Form.Controls("ProgressBar1").Value = 0\par
    End If\par
Else\par
    MessageBox.Show("Web\'b7\'fe\'ce\'f1\'d2\'d1\'c6\'f4\'b6\'af\'a3\'ac\'c7\'eb\'ce\'f0\'d6\'d8\'b8\'b4\'b2\'d9\'d7\'f7\'a3\'a1")\par
End If\par
\par
\cf2 MainFrm_Button_Stop_Click\par
\par
\cf3 '\'cd\'a3\'d6\'b9Web\'b7\'fe\'ce\'f1\par
If HttpServer.IsRunning Then\par
    HttpServer.Close()\par
    e.Form.Controls("ProgressBar1").Value = 0\par
Else\par
    MessageBox.Show("Web\'b7\'fe\'ce\'f1\'d2\'d1\'cd\'a3\'d6\'b9")\par
End If\par
e.Form.Controls("Button_Start").Enabled = True\par
e.Form.Controls("Button_Stop").Enabled = False\par
\par
\cf2 MainFrm_Stock_Click\par
\par
\cf3 Forms("StockSetup").show\par
\par
\cf2 ParametersModal_Button1_Click\par
\par
\cf3 Tables("InternalParameters").Current.Save()\par
e.Form.Close()\par
\par
\cf2 StockSetup_Button1_Click\par
\par
\cf3 Dim hbl As String = e.Form.Controls("TextBox1").Text\par
If hbl = "" Then\par
    MessageBox.Show("HBL\'b2\'bb\'b4\'e6\'d4\'da\'a3\'ac\'c7\'eb\'cf\'c8\'b4\'b4\'bd\'a8\'d2\'bb\'b8\'f6\'c8\'eb\'bf\'e2\'b6\'a9\'b5\'a5\'a3\'a1")\par
    Return\par
End If\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select * From BWInOrderHead Where Del = 0  And HBL = '" & hbl & "'"\par
Dim headDt As DataTable = cmd.ExecuteReader(True)\par
If headDt.DataRows.Count = 0 Then\par
    MessageBox.Show("HBL\'b2\'bb\'b4\'e6\'d4\'da\'a3\'ac\'c7\'eb\'cf\'c8\'b4\'b4\'bd\'a8\'d2\'bb\'b8\'f6\'c8\'eb\'bf\'e2\'b6\'a9\'b5\'a5")\par
    Return\par
End If\par
\par
Dim headDr As DataRow = headDt.DataRows(0)\par
Dim headID As String = headDr("Guid")\par
\par
cmd.CommandText = "Select AreaName,Guid From BaseArea Where Del = 0 And IsActived = 1 And IsDefault = 1 And AreaType = 'I' And WarehouseCode = ?"\par
cmd.Parameters.Add("@WarehouseCode", headDr("WarehouseCode"))\par
Dim Values = cmd.ExecuteValues\par
cmd.Parameters.Clear()\par
If Values.Count > 0 Then\par
    headDr("InAreaName") = Values("AreaName")\par
    headDr("InAreaID") = Values("Guid")\par
End If\par
If headDr.IsNull("InDate") Then\par
    headDr("InDate") = Date.Now() \par
End If \par
headDr("InStatus") = 1\par
headDr("IsClosed") = 1\par
headDr("CloseBy") = "System"\par
headDr("CloseDate") = Date.Now()\par
headDr("Remark") = "\'bf\'e2\'b4\'e6\'b5\'bc\'c8\'eb"\par
\par
' \'b6\'a8\'d2\'e5\'d2\'bb\'b8\'f6OpenFileDialog\par
Dim dlg As New OpenFileDialog\par
' \'c9\'e8\'d6\'c3\'ce\'c4\'bc\'fe\'c9\'b8\'d1\'a1\'c6\'f7\par
dlg.Filter = "Excel\'ce\'c4\'bc\'fe|*.xls*"\par
' \'b4\'a6\'c0\'ed\'b5\'bc\'c8\'eb\'b5\'c4excel\par
If dlg.ShowDialog = DialogResult.Ok Then\par
    Dim Book As New XLS.Book(dlg.FileName)\par
    Dim Sheet As XLS.Sheet = Book.Sheets(0)\par
    ' \'d0\'a3\'d1\'e9\'b5\'bc\'c8\'eb\'ce\'c4\'bc\'fe\'b8\'f1\'ca\'bd\par
    Dim importFields() As String = \{"\'ce\'ef\'c1\'cf\'b1\'e0\'ba\'c5", "\'c5\'fa\'ba\'c5", "\'b2\'fa\'c6\'b7\'ca\'fd\'c1\'bf", "\'b9\'a4\'b3\'a7", "SAP\'ce\'bb\'d6\'c3"\}\par
    For i As Integer = 0 To importFields.Length - 1\par
        If sheet(0, i).Text <> importFields(i) Then\par
            MessageBox.Show("\'b5\'bc\'c8\'eb\'b1\'ed\'b8\'f1\'b8\'f1\'ca\'bd\'b4\'ed\'ce\'f3!")\par
            Return\par
        End If\par
    Next\par
    ' \'b9\'b9\'d4\'ecItem\'c3\'f7\'cf\'b8\'b1\'ed\par
    cmd.CommandText = "Select * From BWInOrderItem Where 1=2"\par
    Dim dt As DataTable = cmd.ExecuteReader(True)\par
    \par
    cmd.CommandText = "SELECT IsNull(MAX(CAST(SUBSTRING(PalletNo, CHARINDEX('-', PalletNo) + 1, LEN(PalletNo) - CHARINDEX('-', PalletNo)) AS INT)),0) AS MaxNumber FROM BWInOrderItem Where Del = 0 And HeadID = '" & headID & "'"\par
    Dim palletNum As Integer = cmd.ExecuteScalar\par
    Dim opID As String = Guid.NewGuid.ToString\par
    For n As Integer = 1 To Sheet.Rows.Count - 1\par
        If sheet(n, 0).Text = "" Then\par
            Continue For\par
        End If\par
        palletNum = palletNum + 1\par
        Dim dr As DataRow = dt.AddNew()\par
        dr("Guid") = Guid.NewGuid.Tostring\par
        dr("HeadID") = headID\par
        dr("CreateBy") = "System"\par
        dr("CreateDate") = Date.Now\par
        dr("HBL") = headDr("HBL")\par
        dr("OwnerCode") = headDr("OwnerCode")\par
        dr("WarehouseCode") = headDr("WarehouseCode")\par
        dr("MatCode") = sheet(n, 0).Text\par
        dr("ReceivedBatch") = sheet(n, 1).Text\par
        dr("EstimatedReceivedQty") = sheet(n, 2).Text\par
        dr("ReceivedQty") = sheet(n, 2).Text\par
        dr("Plant") = sheet(n, 3).Text\par
        dr("StorageLocation") = sheet(n, 4).Text\par
        dr("ReceivedBy") = "System"\par
        dr("ReceivedDate") = Date.Now()\par
        dr("ReceivingStatus") = 1\par
\par
        dr("PalletID") = Guid.NewGuid.ToString\par
        dr("PalletNo") = Right(dr("HBL"), 6) & "-" & palletNum\par
        dr("SSCC") = "SSCC" & Format(Date.Today, "yyMMdd") & palletNum.ToString.PadLeft(4, "0")\par
        dr("BIN") = "V" & Right(Date.Today.Year, 2) & Date.Today.DayOfYear.ToString.PadLeft(3, "0") & "-" & palletNum.ToString.PadLeft(4, "0")\par
        dr("AreaID") = headDr("InAreaID")\par
        dr("AreaName") = headDr("InAreaName")\par
        dr("Invoice") = "INV000001"\par
        dr("InboundGRStatus") = 1\par
        dr("OPID") = opID\par
    Next\par
    Output.Show("x1")\par
    \par
    \par
    Dim sqlName, sourceInternalIP, sourceInternalPort, sourceExternalIP, sourceExternalPort As String \par
    Try\par
        sqlName = _sysParms("SQLServer")("SQLName").ToString\par
        sourceInternalIP = _sysParms("SQLServer")("SourceIP_Internal").ToString\par
        sourceInternalPort = _sysParms("SQLServer")("SourcePort_Internal").ToString\par
        sourceExternalIP = _sysParms("SQLServer")("SourceIP_External").ToString\par
        sourceExternalPort = _sysParms("SQLServer")("SourcePort_External").ToString\par
    Catch ex As Exception \par
        MessageBox.Show("SQL\'c5\'e4\'d6\'c3\'b4\'ed\'ce\'f3\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'a3\'a1")\par
        Return\par
    End Try \par
    \par
    Dim sqlServer As String\par
    If Network.Ping(sourceInternalIP, 1000) Then\par
        '\'ca\'d7\'d1\'a1\'c4\'da\'cd\'f8\'b5\'d8\'d6\'b7\par
        sqlServer = sourceInternalIP & "," & sourceInternalPort\par
    ElseIf Network.Ping(sourceExternalIP, 1000) Then\par
        '\'b4\'ce\'d1\'a1\'cd\'e2\'cd\'f8\'b5\'d8\'d6\'b7\par
        sqlServer = sourceExternalIP & "," & sourceExternalPort\par
    Else\par
        '\'c4\'da\'cd\'e2\'cd\'f8\'be\'f9\'ce\'de\'b7\'a8Ping\'cd\'a8\'a3\'ac\'c5\'d7\'b3\'f6\'cc\'e1\'ca\'be\par
        MessageBox.Show("\'ca\'fd\'be\'dd\'d4\'b4\'c1\'ac\'bd\'d3\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'cd\'f8\'c2\'e7\'bc\'b0\'b2\'ce\'ca\'fd\'c5\'e4\'d6\'c3\'a3\'a1")\par
        Return\par
    End If\par
    \par
    Dim conStr As String = "server=" & sqlServer & ";uid=tpit;pwd=Tpit#123;database=" & sqlName\par
    Dim tran As System.Data.SqlClient.SqlTransaction\par
    Dim conn As New System.Data.SqlClient.SqlConnection(conStr)\par
    Try\par
        conn.Open()\par
        tran = conn.BeginTransaction()\par
        Dim mpList As New List(Of System.Data.SqlClient.SqlBulkCopyColumnMapping)\par
        For Each dc As DataCol In dt.DataCols\par
            mpList.Add( New System.Data.SqlClient.SqlBulkCopyColumnMapping(dc.Name, dc.Name))\par
        Next\par
        Dim copy As New System.Data.SqlClient.SqlBulkCopy(conn, System.Data.SqlClient.SqlBulkCopyOptions.FireTriggers, tran)\par
        For Each mp As System.Data.SqlClient.SqlBulkCopyColumnMapping In mpList\par
            Copy.ColumnMappings.Add(mp)\par
        Next\par
        copy.DestinationTableName = "BWInOrderItem"\par
        copy.BulkCopyTimeout = 30\par
        copy.BatchSize = 100\par
        copy.WriteToServer(dt.basetable)\par
        tran.Commit()\par
    Catch ex As exception\par
        Output.Show(ex.Message)\par
        tran.Rollback()\par
    Finally\par
        conn.Close()\par
    End Try\par
\par
    ' \'b8\'fc\'d0\'c2\'ce\'ef\'c1\'cf\'d0\'c5\'cf\'a2\par
    cmd.CommandText = "Update BWInOrderItem Set iBarCode = m.BarCode, iMatType = m.MatType, iBrandName = m.BrandName, iBrandEName = m.BrandEName, iEName = m.EName, iCName = m.CName, iiOrigin = m.Origin, iSpecifaction = m.Specifaction, iPCB = m.PCB, iSPCB = m.SPCB, iPCL = m.PCL, iPCP = m.PCP, iIsCareful = m.IsCareful, iHsCode = m.HsCode, iDivision = m.Division, iBrand = m.Brand, iOriginName = m.OriginName," & _\par
    " ReceivedBoxNum = (Case When e.PCB > 0 Then CEILING(CAST(i.ReceivedQty AS FLOAT)/e.PCB) ELSE 0 END), ReceivedExpirationDate = (Select ShelfLifeExpirationDate From NEOBatchMaster Where Del = 0 And Material = i.MatCode And Batch = i.BatchNo) " & _\par
    "From BWInOrderItem i Left Join View_BaseMaterialEx m On i.MatCode = m.MatCode And m.Del = 0 Where i.HeadID = ? And i.OPID = ?"\par
    cmd.Parameters.Add("@HeadID", headID)\par
    cmd.Parameters.Add("@OPID", opid)\par
    cmd.ExecuteNonQuery\par
    headDr.Save()\par
    \par
    '\'c9\'fa\'b3\'c9\'c8\'eb\'bf\'e2\'cd\'d0\'c5\'cc\'bc\'c7\'c2\'bc\par
    cmd.CommandText = "Insert Into Record4Pallet(Guid,DocID,HBL,SSCC,PalletType,PalletNo,CreateDate,CreateBy,Del,AreaID,AreaName,VBIN) " & _\par
    "Select PalletID,HeadID,HBL,SSCC,'X',PalletNo,GETDATE(),'System',0,',AreaID,AreaName,BIN From BWInOrderItem Where Del = 0 And HeadID = ? And OPID = ?"\par
    cmd.ExecuteNonQuery\par
\par
\par
    '------------------------------------------\par
    '\'cc\'ed\'bc\'d3\'b5\'bd\'bf\'e2\'b4\'e6\'c3\'f7\'cf\'b8\par
    cmd.CommandText = "Insert Into StockDetail (Guid,Plant,StorageLocation,MatCode,BatchNo,StockQty,FrozenQty,NormalQty,CreateDate,CreateBy,StockStatus,InDetailID,Del,IsWorking,LockedByMovement,LockedByTaking,PalletID,DocNo,ExpirationDate,StoreCName) " & _\par
    "Select NEWID(),Plant,StorageLocation,MatCode,BatchNo,StoreQty,0,StoreQty,GetDate(),'System','Inbound',Guid,0,0,0,0,PalletID,HeadNo,ExpirationDate,StoreCName From BWInOrderItem Where Del = 0 And HeadID = '" & headID & "' And OPID = '" & opID & "'"\par
    cmd.ExecuteNonQuery\par
    \par
    '\'b8\'fc\'d0\'c2\'bf\'e2\'b4\'e6Flow\par
    cmd.CommandText = "Insert Into StockFlow (Guid,FlowType,StockID,StockType,Plant,StorageLocation,MatCode,BatchNo,Qty,OperateDate,OperateBy,Del,PalletID) " & _\par
    "Select NEWID(),'S',Guid,'UNR',Plant,StorageLocation,MatCode,BatchNo,StoreQty,GETDATE(),'System',0,PalletID From BWInOrderItem Where Del = 0 And HeadID = '" & headID & "' And OPID = '" & opID & "'"\par
    cmd.ExecuteNonQuery\par
    \par
End If\par
MessageBox.Show("\'b5\'bc\'c8\'eb\'cd\'ea\'b3\'c9")\par
\par
\cf1\'d7\'d4\'b6\'a8\'d2\'e5\'ba\'af\'ca\'fd\par
\par
\cf2 Auth\\API_AuthCodes\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim res As New JObject\par
\par
Dim userGuid As String = Functions.Execute("WebService_GetJWTUser", e, "sub")\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
' \'bb\'f1\'c8\'a1\'d3\'c3\'bb\'a7\'bd\'c7\'c9\'ab\par
cmd.CommandText = "Select UserRole From SysUser Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", userGuid)\par
Dim userRole As String = cmd.ExecuteScalar\par
cmd.Parameters.Clear\par
Output.Show(111)\par
Dim roleJa As JArray = JArray.Parse(userRole)\par
\par
cmd.CommandText = "Select Guid, Permission From SysRole Where Del = 0 And IsActived = 1"\par
Dim roleDt As DataTable = cmd.ExecuteReader\par
Output.Show(222)\par
\par
Dim PermJa As New JArray\par
For i As Integer = 0 To roleJa.Count - 1\par
    Dim roleDr As DataRow = roleDt.Find("Guid = '" & roleJa(i).Tostring & "'")\par
    If roleDr IsNot Nothing Then\par
        PermJa = New JArray(PermJa.Union(JArray.Parse(roleDr("Permission"))))\par
    End If\par
Next\par
\par
res("data") = PermJa\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 Auth\\API_AuthLogin\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim res As New JObject\par
\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim userName As String = xData("username").ToString\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
'\'b2\'e9\'d5\'d2\'d3\'c3\'bb\'a7\'d0\'c5\'cf\'a2\par
cmd.CommandText = "Select Guid,UserID,UserName,SecPW,IsActived,IsLocked,LastLockedDate From SysUser Where Del = 0 And UserID = ?"\par
cmd.Parameters.Add("@UserID", userName)\par
Dim Values = cmd.ExecuteValues()\par
cmd.Parameters.Clear()\par
If Values.Count = 0 Then\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'b5\'b1\'c7\'b0\'d3\'c3\'bb\'a7\'b2\'bb\'b4\'e6\'d4\'da!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
Output.Show(1)\par
'\'d3\'c3\'bb\'a7\'d7\'b4\'cc\'ac\'c5\'d0\'b6\'cf\par
If Values("IsActived") = False Then\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'b5\'b1\'c7\'b0\'d3\'c3\'bb\'a7\'d2\'d1\'b1\'bb\'cd\'a3\'d3\'c3\'a3\'ac\'c7\'eb\'c1\'aa\'cf\'b5\'b9\'dc\'c0\'ed\'d4\'b1!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
Output.Show(2)\par
'\'d5\'cb\'bb\'a7\'cb\'f8\'b6\'a8\'c5\'d0\'b6\'cf\par
If Values("IsLocked") Then\par
    Dim lockDate As Date = CDate(Values("LastLoginDate"))\par
    Dim lockTime As Integer = 10 - (now - lockDate).TotalMinutes\par
    If lockTime > 0 Then\par
        res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'b5\'b1\'c7\'b0\'d3\'c3\'bb\'a7\'d2\'d1\'b1\'bb\'cf\'de\'d6\'c6\'a3\'ac\'c7\'eb\'d4\'da" & lockTime & "\'b7\'d6\'d6\'d3\'ba\'f3\'d4\'d9\'b4\'ce\'b3\'a2\'ca\'d4!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
End If\par
Output.Show(3)\par
If Values("SecPW") = EncryptText(xData("password"), "Tpit#123", "@yxw") Then\par
    ' \'b7\'cf\'c6\'fa\'d6\'ae\'c7\'b0\'b5\'c4RefreshToken\par
    cmd.CommandText = "Update SysAuthToken Set IsAbandoned = 1 Where IsAbandoned = 0 And UserGuid = '" & Values("Guid") & "'"\par
    cmd.ExecuteNonQuery()\par
    Output.Show(4)\par
    Dim resJo As New JObject\par
    Dim refreshToken As String = Functions.Execute("Sys_JWTTokenCreate", 2880, Values("Guid"), Values("UserID"), Values("UserName")).ToString\par
    resJo("refreshToken") = refreshToken\par
    resJo("accessToken") = Functions.Execute("Sys_JWTTokenCreate", 120, Values("Guid"), Values("UserID"), Values("UserName")).ToString\par
    resJo("username") = Values("UserName").ToString\par
    res("data") = resJo\par
    \par
    ' \'c9\'fa\'b3\'c9\'d0\'c2\'b5\'c4RefreshToken\'b2\'a2\'bc\'c7\'c2\'bc\'a3\'ac\'c4\'ac\'c8\'cf\'d0\'a7\'c6\'da72\'d0\'a1\'ca\'b1\par
    cmd.CommandText = "Insert Into SysAuthToken (UserGuid, UserID, RefreshToken, CreateDate, LastRefreshDate, IsAbandoned) Values(?,?,?,GetDate(),GetDate(),0)"\par
    cmd.Parameters.Add("@UserGuid", Values("Guid"))\par
    cmd.Parameters.Add("@UserID", Values("UserID"))\par
    cmd.Parameters.Add("@RefreshToken", refreshToken)\par
    cmd.ExecuteNonQuery()\par
    cmd.Parameters.Clear()\par
Else\par
    res("error") = "\'d3\'c3\'bb\'a7ID\'bb\'f2\'c3\'dc\'c2\'eb\'b4\'ed\'ce\'f3!"\par
End If\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 Auth\\API_AuthLogout\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim res As New JObject\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Update SysAuthToken Set IsAbandoned = 1 Where IsAbandoned = 0 And RefreshToken Like ?"\par
cmd.Parameters.Add("@RefreshToken", xData("token").ToString)\par
cmd.ExecuteNonQuery\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 Auth\\API_AuthRefreshToken\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim res As New JObject\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
If xData("token").tostring = "" Then\par
    e.StatusCode = 400\par
    res("error") = "\'b5\'c7\'c2\'bc\'c8\'cf\'d6\'a4\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'d6\'d8\'d0\'c2\'b5\'c7\'c2\'bc"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
\par
Dim jwtUser As JObject = Functions.Execute("Sys_JWTDecode", xData("token").tostring)\par
If jwtUser.Property("sub") Is Nothing Then\par
    e.StatusCode = 400\par
    res("error") = "\'b5\'c7\'c2\'bc\'c8\'cf\'d6\'a4\'b9\'fd\'c6\'da\'a3\'ac\'c7\'eb\'d6\'d8\'d0\'c2\'b5\'c7\'c2\'bc"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If \par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
Dim newToken As String = Functions.Execute("Sys_JWTTokenCreate", 120, jwtUser("sub").ToString, jwtUser("id").ToString, jwtUser("name").ToString).ToString\par
cmd.CommandText = "Update SysAuthToken Set LastRefreshDate = GETDATE() Where IsAbandoned = 0 And UserGuid = ?"\par
cmd.Parameters.Add("@UserGuid", jwtUser("sub").ToString)\par
cmd.ExecuteNonQuery\par
res("data") = newToken\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 Auth\\API_AuthUserInfo\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim res As New JObject\par
\par
' \'b4\'a6\'c0\'edToken\par
Dim xtoken As String = e.Request.Headers("Authorization").Substring("Bearer ".Length).Trim()\par
Dim jwtUser As JObject = Functions.Execute("Sys_JWTDecode", xtoken)\par
Dim userGuid As String = jwtUser.Value(Of String)("sub")\par
\par
' \'bb\'f1\'c8\'a1\'d3\'c3\'bb\'a7\'d0\'c5\'cf\'a2\par
Dim resJo As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select UserName,Email From SysUser Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", userGuid)\par
Dim Values = cmd.ExecuteValues\par
If Values.Count > 0 Then\par
    cmd.CommandText = "Select TableName, ColumnOption From Settings4WebColumn Where UserGuid = ?"\par
    Dim colDt As DataTable = cmd.ExecuteReader\par
    resJo("colSettings") = Functions.Execute("Sys_DataTable2Json", colDt, True)\par
    resJo("realName") = Values("UserName").Tostring\par
    resJo("email") = Values("Email").Tostring\par
    resJo("homePath") = "/workspace"\par
    res("data") = resJo\par
Else\par
    e.StatusCode = 401\par
    res("error") = "\'d3\'c3\'bb\'a7\'d0\'c5\'cf\'a2\'bb\'f1\'c8\'a1\'ca\'a7\'b0\'dc!"\par
End If\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 Common\\Download\\API_CommonDownloadCheckFile\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
Dim filePath As String\par
Select Case xData("type").ToString\par
    Case "APIN2W"\par
        cmd.CommandText = "Select ResponseBodyFile From Task4APINEO2WMS Where Del = 0 And Guid = '" & xData("id").ToString & "'"\par
        filePath = cmd.ExecuteScalar\par
        \par
End Select\par
If filePath = "" OrElse FileSys.FileExists(filePath) = False Then\par
    res("error") = "\'ce\'c4\'bc\'fe\'b2\'bb\'b4\'e6\'d4\'da!"\par
End If\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 Common\\Download\\API_CommonDownloadGetFile\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
Dim filePath As String\par
Select Case xData("type").ToString\par
    Case "APIN2W"\par
        cmd.CommandText = "Select ResponseBodyFile From Task4APINEO2WMS Where Del = 0 And Guid = '" & xData("id").ToString & "'"\par
        filePath = cmd.ExecuteScalar\par
        \par
End Select\par
If filePath = "" OrElse FileSys.FileExists(filePath) = False Then\par
    res("error") = "\'ce\'c4\'bc\'fe\'b2\'bb\'b4\'e6\'d4\'da!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
\par
e.WriteFile(filePath)\par
\par
\cf2 Common\\Download\\API_CommonDownloadTemplateFile\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
\par
Dim filePath As String\par
Select Case xData("typ").ToString\par
    Case "StockSetup"\par
        filePath = ProjectPath & "Template\\StockSetup.xlsx"\par
        \par
End Select\par
If filePath = "" OrElse FileSys.FileExists(filePath) = False Then\par
    res("error") = "\'ce\'c4\'bc\'fe\'b2\'bb\'b4\'e6\'d4\'da!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
\par
e.WriteFile(filePath)\par
\par
\cf2 Common\\API_CommonGetOptions\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
\par
\par
Select Case xData("type").ToString\par
    Case "ConsumerErrorCode"\par
        cmd.ConnectionName = "log"\par
        cmd.CommandText = "Select Distinct ErrorCode as label, ErrorCode as value From Log4Consumer Where ErrorCode <> '' Order By ErrorCode"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        res("data") = Functions.Execute("Sys_DataTable2Json", dt, true)\par
    Case "BWInOrderCreate"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select OwnerCode As value, OwnerShortName As label, 'OwnerCode' As type From BaseOwner Where Del = 0 And IsActived = 1 Union " & _\par
        "Select WarehouseCode As value, WarehouseName As Label, 'WarehouseCode' As type From BaseWarehouse Where Del = 0 And IsActived = 1 Order By label"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        res("data") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
    Case "EventListOptions"\par
        cmd.ConnectionName = "sys"\par
        Dim ja As New Jarray\par
        res("data") = ja\par
        cmd.CommandText = "Select TopicName From Settings4NEOKafkaTopics Where Del = 0" \par
        Dim topicDt As DataTable = cmd.ExecuteReader\par
        For Each topicDr As DataRow In topicDt.DataRows\par
            Dim jo As New JObject\par
            ja.Add(jo)\par
            jo("label") = topicDr("TopicName").ToString\par
            jo("value") = topicDr("TopicName").ToString\par
            jo("type") = "Topic"\par
        Next\par
        Dim exJo1 As New JObject\par
        Dim exJo2 As New JObject\par
        ja.Add(exJo1)\par
        ja.Add(exJo2)\par
        exJo1("label") = "Needless"\par
        exJo2("label") = "Pending"\par
        exJo1("value") = -1\par
        exJo2("value") = 0\par
        exJo1("type") = "CallStatus"\par
        exJo2("type") = "CallStatus"\par
        cmd.CommandText = "Select Distinct CallStatus From NEOEvent Where Del = 0 And CallStatus > 0"\par
        Dim statusDt As DataTable = cmd.ExecuteReader\par
        For Each statusDr As DataRow In statusDt.DataRows\par
            Dim jo As New JObject\par
            ja.Add(jo)\par
            jo("label") = statusDr("CallStatus").ToString\par
            jo("value") = Val(statusDr("CallStatus"))\par
            jo("type") = "CallStatus"\par
        Next\par
    Case "NEOAPIList"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select Guid As id, APIName As name, APIURL as url, APIMethod as method From Settings4NEOAPI Where Del = 0 And IsActived = 1 Order By APIType, _SortKey"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        res("data") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
    Case "Log4APINEO2WMS"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select APIName As label, APIName As value, 'APIName' As type From Settings4NEOAPI Where Del = 0 And APIType = 'NEO2WMS' Union " & _\par
        "Select StatusCodeName as label, CAST(StatusCode AS VARCHAR) as value, 'StatusCode' as type From " & _\par
        "(Select StatusCode, (Case StatusCode When 0 Then 'Pending' Else CAST(StatusCode AS VARCHAR) End) As StatusCodeName From (Select DISTINCT StatusCode From Task4APINEO2WMS Where Del = 0)tmp1)tmp2 Order By value"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        res("data") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
    Case "Log4APIWMS2NEO"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select APIName As label, APIName As value, 'APIName' As type From Settings4NEOAPI Where Del = 0 And APIType = 'WMS2NEO' Union " & _\par
        "Select StatusCodeName as label, CAST(StatusCode AS VARCHAR) as value, 'StatusCode' as type From " & _\par
        "(Select StatusCode, (Case StatusCode When 0 Then 'Pending' Else CAST(StatusCode AS VARCHAR) End) As StatusCodeName From (Select DISTINCT StatusCode From Task4APIWMS2NEO Where Del = 0)tmp1)tmp2 Order By value"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        res("data") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
        \par
    Case "Log4APIWMS2LIS"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select APIDescription As label, APIDescription As value, 'APIDescription' As type From Settings4LISAPIItem Where Del = 0 Union " & _\par
        "Select StatusCodeName as label, CAST(StatusCode AS VARCHAR) as value, 'StatusCode' as type From " & _\par
        "(Select StatusCode, (Case StatusCode When 0 Then 'Pending' Else CAST(StatusCode AS VARCHAR) End) As StatusCodeName From (Select DISTINCT StatusCode From Task4APIWMS2LIS Where Del = 0)tmp1)tmp2 Order By value"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        res("data") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
        \par
    Case "StockDetail"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select BrandName As value, BrandName As label, 'BrandName' As type From BaseBrand Where Del = 0 Union " & _\par
        "Select OwnerShortName As value, OwnerShortName As label, 'OwnerShortName' As type From BaseOwner Where Del = 0 Union " & _\par
        "Select WarehouseName As value, WarehouseName As label, 'WarehouseName' As type From BaseWarehouse Where Del = 0  Union " & _\par
        "Select DictionaryName As value, DictionaryName As label, 'OriginName' As type From BaseDictionary Where Del = 0 And DictionaryType = 'Origin' Order By label"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        Dim ja As New JArray \par
        ja = Functions.Execute("Sys_DataTable2Json", dt, False)\par
        Dim drs As List(Of DataRow) = DataTables("InternalParameters").Select("ParameterType In ('StockStatus','Storagelocation','MaterialType','Division') And IsActived = 1", "_SortKey")\par
        For Each dr As DataRow In drs\par
            Dim jo As New JObject\par
            ja.Add(jo)\par
            jo("label") = dr("Description").ToString\par
            jo("value") = dr("ParameterValue").ToString\par
            jo("type") = dr("ParameterType").ToString\par
        Next\par
        \par
        res("data") = ja\par
        \par
    Case "StockOverView"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select OwnerShortName As value, OwnerShortName As label, 'OwnerShortName' As type From BaseOwner Where Del = 0"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        Dim ja As New JArray \par
        ja = Functions.Execute("Sys_DataTable2Json", dt, False)\par
        Dim drs As List(Of DataRow) = DataTables("InternalParameters").Select("ParameterType In ('Storagelocation','MaterialType') And IsActived = 1", "_SortKey")\par
        For Each dr As DataRow In drs\par
            Dim jo As New JObject\par
            ja.Add(jo)\par
            jo("label") = dr("Description").ToString\par
            jo("value") = dr("ParameterValue").ToString\par
            jo("type") = dr("ParameterType").ToString\par
        Next\par
        \par
        res("data") = ja\par
    Case "Record4BWInboundReceived"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select BrandName As value, BrandName As label, 'iBrandName' As type From BaseBrand Where Del = 0 Union " & _\par
        "Select OwnerShortName As value, OwnerShortName As label, 'OwnerShortName' As type From BaseOwner Where Del = 0 Union " & _\par
        "Select WarehouseName As value, WarehouseName As label, 'WarehouseName' As type From BaseWarehouse Where Del = 0  Union " & _\par
        "Select DictionaryName As value, DictionaryName As label, 'iOriginName' As type From BaseDictionary Where Del = 0 And DictionaryType = 'Origin' Order By label"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        Dim ja As New JArray \par
        ja = Functions.Execute("Sys_DataTable2Json", dt, False)\par
        Dim drs As List(Of DataRow)\par
        drs = DataTables("InternalParameters").Select("ParameterType In ('MaterialType') And IsActived = 1", "_SortKey")\par
        For Each dr As DataRow In drs\par
            Dim jo As New JObject\par
            ja.Add(jo)\par
            jo("label") = dr("Description").ToString\par
            jo("value") = dr("ParameterValue").ToString\par
            jo("type") = "iMatType"\par
        Next\par
        drs = DataTables("InternalParameters").Select("ParameterType In ('Division') And IsActived = 1", "_SortKey")\par
        For Each dr As DataRow In drs\par
            Dim jo As New JObject\par
            ja.Add(jo)\par
            jo("label") = dr("Description").ToString\par
            jo("value") = dr("ParameterValue").ToString\par
            jo("type") = "iDivision"\par
        Next\par
        \par
        res("data") = ja\par
    Case "DictionaryType"\par
        Dim ja As New JArray\par
        Dim drs As List(Of DataRow) = DataTables("InternalParameters").Select("ParameterType = 'DictionaryType' And IsActived = 1", "_SortKey")\par
        For Each dr As DataRow In drs\par
            Dim jo As New JObject\par
            ja.Add(jo)\par
            jo("label") = dr("ParameterName").ToString\par
            jo("value") = dr("ParameterValue").ToString\par
            \par
        Next\par
        res("data") = ja\par
    Case "OutOrderList"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select OwnerShortName As value, OwnerShortName As label, 'OwnerShortName' As type From BaseOwner Where Del = 0 Union " & _\par
        "Select WarehouseName As value, WarehouseName As Label, 'WarehouseName' As type From BaseWarehouse Where Del = 0  Order By label"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        Dim ja As New JArray \par
        ja = Functions.Execute("Sys_DataTable2Json", dt, False)\par
        Dim drs As List(Of DataRow) = DataTables("InternalParameters").Select("ParameterType ='OutDeliveryType' And IsActived = 1", "_SortKey")\par
        For Each dr As DataRow In drs\par
            Dim jo As New JObject\par
            ja.Add(jo)\par
            jo("label") = dr("Description").ToString\par
            jo("value") = dr("ParameterValue").ToString\par
            jo("type") = dr("ParameterType").ToString\par
        Next\par
        \par
        res("data") = ja\par
    Case "BWOutOrderCreate"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select OwnerCode As value, OwnerShortName As label, 'OwnerCode' As type From BaseOwner Where Del = 0 Union " & _\par
        "Select WarehouseCode As value, WarehouseName As Label, 'WarehouseCode' As type From BaseWarehouse Where Del = 0 Union " & _\par
        "Select InHeadID as value, HBL as label, 'HBL' As type From StockDetail Where Del = 0 And IsWorking = 0 And StockStatus = 'Inbound' And UnrestrictedQty > 0 Group By HBL,InHeadID Order By label"\par
        \par
        Dim dt As DataTable = cmd.ExecuteReader\par
        Dim ja As New JArray\par
        ja = Functions.Execute("Sys_DataTable2Json", dt, False)\par
        Dim drs As List(Of DataRow) = DataTables("InternalParameters").Select("ParameterType = 'OutDeliveryType' And IsActived = 1", "_SortKey")\par
        For Each dr As DataRow In drs\par
            Dim jo As New JObject\par
            ja.Add(jo)\par
            jo("label") = dr("ParameterName").ToString\par
            jo("value") = dr("ParameterValue").ToString\par
            jo("type") = "OutDeliveryType"\par
        Next\par
        res("data") = ja\par
        \par
    Case "BWOutOrderCreateByHBL"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select HBL as label,InHeadID as value From StockDetail Where Del = 0 And IsWorking = 0 And StockStatus = 'Inbound' And UnrestrictedQty > 0 Group By HBL,InHeadID Order By HBL"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        res("data") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
        \par
    Case "BWOutPickingCreate"\par
        cmd.ConnectionName = "sys"\par
        cmd.CommandText = "Select OwnerCode As value, OwnerShortName As label, 'OwnerCode' As type From BaseOwner Where Del = 0 Union " & _\par
        "Select WarehouseCode As value, WarehouseName As Label, 'WarehouseCode' As type From BaseWarehouse Where Del = 0 Union " & _\par
        "Select OutHeadID as value, BLNo as label, 'BLNo' as type From StockDetail Where Del = 0 And IsWorking = 0 And StockStatus = 'OutTally' And UnrestrictedQty > 0 Group By BLNo,OutHeadID Order By label"\par
        Dim dt As DataTable = cmd.ExecuteReader\par
        res("data") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
    Case Else\par
        res("data") = New Jarray\par
        If xData("type").ToString Like "StockTaking_*" Then\par
            Dim warehouseCode As String = xData("type").ToString.Replace("StockTaking_", "")\par
            cmd.ConnectionName = "sys"\par
            cmd.CommandText = "Select BrandName As value, BrandName As label, 'BrandName' As type From BaseBrand Where Del = 0 Union " & _\par
            "Select AreaName As value, AreaName As label, 'AreaName' As type From BaseArea Where Del = 0 And WarehouseCode = '" & warehouseCode & "'"\par
            Dim dt As DataTable = cmd.ExecuteReader\par
            Dim ja As New JArray \par
            ja = Functions.Execute("Sys_DataTable2Json", dt, False)\par
            Dim drs As List(Of DataRow) = DataTables("InternalParameters").Select("ParameterType In ('StockStatus','Storagelocation','MaterialType','Division') And IsActived = 1", "_SortKey")\par
            For Each dr As DataRow In drs\par
                Dim jo As New JObject\par
                ja.Add(jo)\par
                jo("label") = dr("Description").ToString\par
                jo("value") = dr("ParameterValue").ToString\par
                jo("type") = dr("ParameterType").ToString\par
            Next\par
            \par
            res("data") = ja\par
        End If \par
End Select \par
\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 Auth\\API_MenuAll\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim res As New JObject\par
\par
Dim userGuid As String = Functions.Execute("WebService_GetJWTUser", e, "sub")\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
' \'bb\'f1\'c8\'a1\'d3\'c3\'bb\'a7\'bd\'c7\'c9\'ab\par
cmd.CommandText = "Select UserRole From SysUser Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", userGuid)\par
Dim userRole As String = cmd.ExecuteScalar\par
cmd.Parameters.Clear\par
Dim roleJa As JArray = JArray.Parse(userRole)\par
\par
' \'bb\'f1\'c8\'a1\'d3\'c3\'bb\'a7\'bd\'c7\'c9\'ab\'d6\'d0\'b0\'fc\'ba\'ac\'b5\'c4\'cb\'f9\'d3\'d0\'c2\'b7\'d3\'c9\'c3\'fb\'b3\'c6\par
cmd.CommandText = "Select Guid, Route From SysRole Where Del = 0 And IsActived = 1"\par
Dim roleDt As DataTable = cmd.ExecuteReader\par
Dim routeNameJa As New JArray\par
routeNameJa.Add("Workspace")\par
\par
For i As Integer = 0 To roleJa.Count - 1\par
    Dim dr As DataRow = roleDt.Find("Guid = '" & roleJa(i).Tostring & "'")\par
    If dr IsNot Nothing Then\par
        routeNameJa = New JArray(routeNameJa.Union(JArray.Parse(dr("Route"))))\par
    End If\par
Next\par
\par
For i As Integer = 0 To routeNameJa.count - 1\par
    Functions.Execute("Func_AddParentRoute", routeNameJa, routeNameJa(i).Tostring)\par
Next\par
\par
\par
Dim routerJa As New JArray\par
For Each routeDr As DataRow In DataTables("InternalRouter").DataRows\par
    If routeDr("Level") = 0 And routeDr("Del") = 0 Then\par
        'Functions.Execute("Func_HandleAllRoute", DataTables("InternalRouter"), routeDr, routerJa)\par
        Functions.Execute("Func_HandleRoute", DataTables("InternalRouter"), routeNameJa, routeDr, routerJa)\par
    End If\par
Next\par
\par
\par
res("data") = routerJa\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BaseMaterial\\BaseMaterialGetData\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
\par
' \'b6\'a8\'d2\'e5\'ce\'d2\'c3\'c7\'d2\'aa\'b2\'e9\'d5\'d2\'b5\'c4 FNC1 \'d7\'d6\'b7\'fb\par
'Dim fnc1Char As Char = Chr(29)\par
\par
' \'ca\'b9\'d3\'c3 Contains \'b7\'bd\'b7\'a8\'bc\'ec\'b2\'e2\par
'If xData("id").ToString.Contains(fnc1Char) Then\par
'Output.Show("\'cc\'f5\'c2\'eb\'ca\'fd\'be\'dd\'d6\'d0\'b0\'fc\'ba\'ac FNC1 (GS, Chr(29)) \'b7\'d6\'b8\'f4\'b7\'fb\'a1\'a3")\par
' \'d4\'da\'d5\'e2\'c0\'ef\'bf\'c9\'d2\'d4\'bc\'cc\'d0\'f8\'b4\'a6\'c0\'ed\'a3\'ac\'c0\'fd\'c8\'e7\'b7\'d6\'b8\'ee\'d7\'d6\'b7\'fb\'b4\'ae\par
'Else\par
'Output.Show("\'cc\'f5\'c2\'eb\'ca\'fd\'be\'dd\'d6\'d0\'b2\'bb\'b0\'fc\'ba\'ac FNC1 \'b7\'d6\'b8\'f4\'b7\'fb\'a1\'a3")\par
'End If\par
\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select p.*,(Select BrandName From BaseBrand Where Del = 0 And SapS4Code = p.signatureSapS4Code) As BrandName From NEOProductMaster p Where p.Del = 0 And (p.skuCode = ? Or productStandardId = ?)"\par
cmd.Parameters.Add("@skuCode", xData("id").ToString)\par
cmd.Parameters.Add("@productStandardId", xData("id").ToString)\par
Dim dt As DataTable = cmd.ExecuteReader\par
res("data") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWCheckBatch\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select FORMAT(ShelfLifeExpirationDate, 'yyyy-MM-dd') AS ShelfLifeExpirationDate From View_NEOBatchMasterWithSub Where Del = 0 And Material = ? And SubBatch = ?"\par
cmd.Parameters.Add("@Material", xData("material").ToString)\par
cmd.Parameters.Add("@SubBatch", xData("batch").ToString)\par
Dim dt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
If dt.DataRows.Count = 0 Then\par
    res("error") = "\'c5\'fa\'b4\'ce\'d6\'f7\'ca\'fd\'be\'dd\'b2\'bb\'b4\'e6\'d4\'da\'a3\'ac\'c7\'eb\'c1\'f4\'d2\'e2\'ca\'b5\'ce\'ef\'c5\'fa\'b4\'ce\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
Else\par
    res("data") = dt.DataRows(0)("ShelfLifeExpirationDate").ToString\par
End If\par
\par
\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWCSARQC\\BWInCSARQCGetData\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select * From Record4CSARQC Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", xData("id").ToString)\par
Dim dt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
If dt.DataRows.Count = 0 Then\par
    res("error") = "\'b4\'fd\'bc\'ec\'c3\'f7\'cf\'b8\'b2\'bb\'b4\'e6\'d4\'da\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
Else\par
    res("data") = Functions.Execute("Sys_DataRow2JObject", dt.DataRows(0), False)\par
End If\par
\par
\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWCSARQC\\BWInCSARQCGetList\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "(Select r.*,m.BarCode As iBarCode From Record4CSARQC r Left Join BaseMaterial m On r.MatCode = m.MatCode And m.Del = 0) tmp", "Guid,HBL,MatCode,BatchNo,iBarCode", " And Del = 0 And Status = 0", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWCSARQC\\BWInCSARQCItemUpdate\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
Dim res As New JObject\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
cmd.CommandText = "Select * From Record4CSARQC Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", xData("id").tostring)\par
Dim itemDt As DataTable = cmd.ExecuteReader(True)\par
\par
If itemDt.DataRows.Count = 0 Then\par
    res("error") = "\'c3\'f7\'cf\'b8\'d7\'b4\'cc\'ac\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'b7\'b5\'bb\'d8\'b2\'e9\'bf\'b4!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
Else\par
    Dim itemDr As DataRow = itemDt.DataRows(0)\par
    itemDr("Status") = 1\par
    itemDr("UpdateBy") = userName\par
    itemDr("UpdateDate") = Date.Now\par
    For Each item As Object In xData\par
        If itemDt.DataCols.Contains(item.key) Then\par
            itemDr(item.key) = item.value\par
        End If\par
    Next\par
    itemDr.Save()\par
End If\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWCSARQC\\BWInGetUnQCHBL\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "(Select HeadID As _Identify,HeadID,HBL,Count(Guid) As Count From Record4CSARQC Where Del = 0 And Status = 0 Group By HeadID,HBL) tmp", "*", "", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWIn\\BWInOrderGetData\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As New JObject\par
res("data") = resJo\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select * From View_BWInOrderHeadEx Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", xData("id").ToString)\par
Dim dt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
If dt.DataRows.Count = 0 Then\par
    res("error") = "HBL\'b2\'bb\'b4\'e6\'d4\'da\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
Else\par
    resJo("info") = Functions.Execute("Sys_DataRow2JObject", dt.DataRows(0), False)\par
End If\par
\par
\par
' \'b4\'a6\'c0\'ed\'bf\'e2\'c7\'f8\'d1\'a1\'cf\'ee\par
cmd.CommandText = "Select AreaName as label,Guid as value,IsDefault as d From BaseArea Where Del = 0 And IsActived = 1 And AreaType = 'I' And WarehouseCode = ? Order By AreaName"\par
cmd.Parameters.Add("@WarehouseCode", dt.DataRows(0)("WarehouseCode"))\par
Dim areaDt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
resJo("areaOptions") = Functions.Execute("Sys_DataTable2Json", areaDt, False)\par
\par
' \'b4\'a6\'c0\'ed\'c4\'ac\'c8\'cf\'d6\'b5\par
Dim deafultAreaDr As DataRow = areaDt.Find("d = 1")\par
If deafultAreaDr IsNot Nothing Then\par
    resJo("info")("InArea") = deafultAreaDr("value").ToString\par
End If \par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWReceiving\\BWInOrderGetReceivingData\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select ReceivingStatus,Guid,HBL,InDate,SSCC,PalletNo,MatCode,iBarCode,iMatType,iPCB,iSPCB,iPCL,iPCP,InboundBatch,EstimatedReceivedQty,iSpecifaction,iCName,iOriginName,ISNULL((Select RelatedBatches From NEOBatchMaster Where Del = 0 And Material = MatCode And Batch = InboundBatch),'\'ce\'de') As RelatedBatches," & _\par
"(Case When isBatchManagementRequired = 1 Then '\'ca\'c7' Else '\'b7\'f1' End) As isBatchManagementRequired,(Case When iIsCareful = 1 Then '\'ca\'c7' Else '\'b7\'f1' End) As iIsCareful,lifespanInDays,PCLInWMS,PCPInWMS,MaterialRemark From View_BWInOrderItemEx Where Del = 0 And Guid = ? Or OriginID = ?"\par
cmd.Parameters.Add("@Guid1", xData("id").ToString)\par
cmd.Parameters.Add("@Guid2", xData("id").ToString)\par
Dim dt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
If dt.DataRows.Count = 0 Then\par
    res("error") = "\'ca\'d5\'bb\'f5\'c3\'f7\'cf\'b8\'b2\'bb\'b4\'e6\'d4\'da\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
Else\par
    res("data") = Functions.Execute("Sys_DataRow2JObject", dt.DataRows(0), False)\par
End If\par
\par
\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWIn\\BWInOrderGetUnInList\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "BWInOrderHead", "Guid,HBL,CreateDate,PreInDate", " And Del = 0 And InStatus = 0", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWReceiving\\BWInOrderGetUnReceiveHBL\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "View_MB_BWInOrderUnReceived", "*", "", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWIn\\BWInOrderInStatusUpdate\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
' \'d0\'a3\'d1\'e9\'c8\'eb\'bf\'e2\'b6\'a9\'b5\'a5\'ca\'c7\'b7\'f1\'ca\'d5\'bb\'f5\par
cmd.CommandText = "Select _Identify From BWInOrderItem Where Del = 0 And ReceivingStatus = 1 And HeadID = ?"\par
cmd.Parameters.Add("@Guid", xData("id").tostring)\par
If cmd.ExecuteScalar > 0 Then\par
    res("error") = "\'c8\'eb\'bf\'e2\'b6\'a9\'b5\'a5\'d2\'d1\'bf\'aa\'ca\'bc\'ca\'d5\'bb\'f5\'a3\'ac\'ce\'de\'b7\'a8\'d0\'de\'b8\'c4\'bd\'f8\'b2\'d6\'ca\'fd\'be\'dd!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
cmd.Parameters.Clear()\par
\par
cmd.CommandText = "Select * From BWInOrderHead Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", xData("id").ToString)\par
Dim dt As DataTable = cmd.ExecuteReader(True)\par
cmd.Parameters.Clear\par
If dt.DataRows.Count = 0 Then\par
    res("error") = "HBL\'b2\'bb\'b4\'e6\'d4\'da\'a3\'a1"\par
Else\par
    Dim dr As DataRow = dt.DataRows(0)\par
    dr("InDate") = CDate(xData("inDate").ToString)\par
    If dr.IsNull("PreInDate") Then\par
        dr("PreInDate") = dr("InDate")\par
    End If \par
    dr("InConfirmDate") = now\par
    dr("InConfirmBy") = userName\par
    dr("InStatus") = 1\par
    \par
    cmd.CommandText = "Select AreaName From BaseArea Where Guid = ?"\par
    cmd.Parameters.Add("@Guid", xData("inArea").tostring)\par
    Dim inAreaName As String = cmd.ExecuteScalar\par
    cmd.Parameters.Clear\par
    \par
    dr("InAreaID") = xData("inArea").ToString\par
    dr("InAreaName") = inAreaName\par
    dr.Save()\par
End If\par
\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWReceiving\\BWInOrderItemReceiving\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
Try\par
    cmd.BeginTransaction()\par
    Dim receivedID As String = xData("id").tostring\par
    cmd.CommandText = "Select * From BWInOrderItem Where Del = 0 And ReceivingStatus = 0 And Guid = ?"\par
    cmd.Parameters.Add("@Guid", receivedID)\par
    Dim itemDt As DataTable = cmd.ExecuteReader(True)\par
    \par
    If itemDt.DataRows.Count = 0 Then\par
        res("error") = "\'c3\'f7\'cf\'b8\'d7\'b4\'cc\'ac\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    ' \'bb\'f1\'c8\'a1\'b1\'ed\'cd\'b7\'d0\'c5\'cf\'a2\par
    cmd.CommandText = "Select CAST(InStatus AS INT) AS InStatus From BWInOrderHead Where Del = 0 And Guid = (Select HeadID From BWInOrderItem Where Del = 0 And Guid = ?)"\par
    Dim Values = cmd.ExecuteValues\par
    cmd.Parameters.Clear()\par
    If Values.Count = 0 Then\par
        res("error") = "\'b6\'a9\'b5\'a5\'b1\'ed\'cd\'b7\'bb\'f1\'c8\'a1\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    If Values("InStatus") = 0 Then\par
        res("error") = "\'c7\'eb\'cf\'c8\'cd\'ea\'b3\'c9\'bd\'f8\'b2\'d6\'c8\'b7\'c8\'cf!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    Dim itemDr As DataRow = itemDt.DataRows(0)\par
    itemDr("UpdateBy") = userName\par
    itemDr("ReceivedBy") = userName\par
    itemDr("UpdateDate") = Date.Now\par
    itemDr("ReceivedDate") = Date.Now\par
    itemDr("ReceivedBoxNum") = Val(xData("ReceivedBoxNum"))\par
    itemDr("ReceivedQty") = Val(xData("ReceivedQty"))\par
    itemDr("ReceivedBatch") = xData("ReceivedBatch").tostring.Trim.ToUpper\par
    itemDr("ReceivedExpirationDate") = xData("ReceivedExpirationDate").tostring.Trim\par
    itemDr("Remark") = xData.Value(Of String)("Remark")\par
    itemDr("ReceivingStatus") = 1\par
    \par
    ' \'b3\'ac\'ca\'d5\'d0\'a3\'d1\'e9\par
    If itemDr("ReceivedQty") > itemDr("EstimatedReceivedQty") Then\par
        res("error") = "\'ca\'b5\'ca\'d5\'ca\'fd\'c1\'bf\'b2\'bb\'c4\'dc\'b4\'f3\'d3\'da\'b2\'fa\'c6\'b7\'ca\'fd\'a3\'ac\'b6\'e0\'b7\'a2\'bb\'f5\'c7\'eb\'d3\'eb\'bb\'f5\'d6\'f7\'c8\'b7\'c8\'cf!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    ' \'c9\'d9\'bb\'f5/\'b2\'f0\'b7\'d6\'b4\'a6\'c0\'ed\par
    Dim missingQty As Integer = itemDr("EstimatedReceivedQty") - CInt(itemDr("ReceivedQty"))\par
    Select Case xData("typ").ToString\par
        Case 0\par
            If missingQty > 0 Then\par
                res("error") = "\'ca\'fd\'be\'dd\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
                Functions.Execute("WebService_APIResult", e, res)\par
                Return ""\par
            End If\par
            ' \'c8\'ab\'c1\'bf\'ca\'d5\'bb\'f5\'a3\'ac\'b8\'b3\'d6\'b5\'d0\'c2\'cd\'d0\'c5\'cc\'ba\'c5\par
            If xData.Property("PalletNo") IsNot Nothing Then\par
                itemDr("PalletNo") = xData("PalletNo")\par
            End If \par
        Case 1 '\'b2\'f0\'b7\'d6\'b4\'a6\'c0\'ed\par
            If missingQty = 0 Then\par
                res("error") = "\'ca\'fd\'be\'dd\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
                Functions.Execute("WebService_APIResult", e, res)\par
                Return ""\par
            End If\par
            \par
            ' \'b1\'ea\'bc\'c7\'c9\'be\'b3\'fd\'d4\'ad\'ca\'bcItem\par
            itemDr("MissingQty") = 0\par
            itemDr("Del") = 1\par
            ' \'d2\'d1\'ca\'b5\'ca\'d5\'ca\'fd\'c1\'bf\'b4\'b4\'bd\'a8\'d0\'c2Item\'b2\'a2\'d7\'f6\'ca\'d5\'bb\'f5\'b4\'a6\'c0\'ed\par
            Dim newDr As DataRow = itemDt.AddNew()\par
            newDr = itemDr.Clone\par
            receivedID = Guid.NewGuid.ToString()\par
            newDr("Guid") = receivedID\par
            newDr("Del") = 0\par
            newDr("SplitLevel") = itemDr("SplitLevel") + 1\par
            newDr("EstimatedReceivedQty") = itemDr("ReceivedQty")\par
            newDr("ReceivingStatus") = 1\par
            ' \'b2\'f0\'b7\'d6\'ca\'d5\'bb\'f5\'a3\'ac\'b8\'b3\'d6\'b5\'d0\'c2\'cd\'d0\'c5\'cc\'ba\'c5\par
            If xData.Property("PalletNo") IsNot Nothing Then\par
                newDr("PalletNo") = xData("PalletNo")\par
            End If \par
            newDr.Save()\par
            \par
            ' \'d2\'d1\'c9\'d9\'bb\'f5\'ca\'fd\'c1\'bf\'b4\'b4\'bd\'a8\'b2\'f0\'b7\'d6Item\'a3\'ac\'b2\'bb\'d7\'f6\'ca\'d5\'bb\'f5\'b4\'a6\'c0\'ed\par
            Dim splitDr As DataRow = itemDt.AddNew()\par
            splitDr = itemDr.Clone\par
            splitDr("Guid") = Guid.NewGuid.ToString()\par
            splitDr("Del") = 0\par
            splitDr("SplitLevel") = itemDr("SplitLevel") + 1\par
            splitDr("EstimatedReceivedQty") = missingQty\par
            splitDr("ReceivingStatus") = 0\par
            splitDr("CreateBy") = userName\par
            splitDr("CreateDate") = Date.Now\par
            Dim unCopyFields() As String = \{"ReceivedQty", "ReceivedBoxNum", "ReceivedExpirationDate", "ReceivedBatch", "Remark", "ReceivedBy", "ReceivedDate"\}\par
            For i As Integer = 0 To unCopyFields.Length - 1\par
                splitDr(unCopyFields(i)) = Nothing\par
            Next\par
            ' \'b2\'f0\'b7\'d6\'ca\'a3\'d3\'e0\'a3\'ac\'b8\'b3\'d6\'b5\'d0\'c2\'cd\'d0\'c5\'cc\'ba\'c5\par
            'If xData.Property("PalletNo") IsNot Nothing Then\par
                'splitDr("PalletNo") = xData("PalletNo")\par
            'End If \par
            splitDr.save()\par
            \par
            ' \'b4\'b4\'bd\'a8\'b2\'f0\'b7\'d6\'bc\'c7\'c2\'bc\par
            Dim parentID As String\par
            ' \'b2\'e9\'d1\'af\'ca\'d5\'bb\'f5item\'b1\'be\'c9\'ed\'ca\'c7\'b7\'f1\'b2\'f0\'b7\'d6\'b9\'fd\'a3\'ac\'bb\'f1\'c8\'a1\'c9\'cf\'bc\'b6\'b5\'c4\'b2\'f0\'b7\'d6ID\par
            cmd.CommandText = "Select Guid From Record4BWInOrderItemSplit Where Del = 0 And (NewID = '" & itemDr("Guid") & "' Or SplitID = '" & itemDr("Guid") & "')"\par
            parentID = cmd.ExecuteScalar\par
            cmd.CommandText = "Insert Into Record4BWInOrderItemSplit (Del,Guid,CreateDate,CreateBy,HeadID,OriginID,OriginQty,NewID,NewQty,SplitID,SplitQty,MatCode,InboundBatch,InboundItemID,InboundHeadID,ParentID) Values (0,NEWID(),GETDATE(),?,?,?,?,?,?,?,?,?,?,?,?,?)"\par
            cmd.Parameters.Add("@CreateBy", userName)\par
            cmd.Parameters.Add("@HeadID", itemDr("HeadID"))\par
            cmd.Parameters.Add("@OriginID", itemDr("Guid"))\par
            cmd.Parameters.Add("@OriginQty", itemDr("EstimatedReceivedQty"))\par
            cmd.Parameters.Add("@NewID", newDr("Guid"))\par
            cmd.Parameters.Add("@NewQty", newDr("EstimatedReceivedQty"))\par
            cmd.Parameters.Add("@SplitID", splitDr("Guid"))\par
            cmd.Parameters.Add("@SplitQty", splitDr("EstimatedReceivedQty"))\par
            cmd.Parameters.Add("@MatCode", itemDr("MatCode"))\par
            cmd.Parameters.Add("@InboundBatch", itemDr("InboundBatch"))\par
            cmd.Parameters.Add("@InboundItemID", itemDr("InboundItemID"))\par
            cmd.Parameters.Add("@InboundHeadID", itemDr("InboundHeadID"))\par
            cmd.Parameters.Add("@ParentID", IIf(parentID = "", Nothing, parentID))\par
            cmd.ExecuteNonQuery\par
            cmd.Parameters.Clear\par
            Output.Show("Insert 3")\par
        Case 2 '\'c9\'d9\'bb\'f5\'b4\'a6\'c0\'ed\par
            If missingQty = 0 Then\par
                res("error") = "\'ca\'fd\'be\'dd\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
                Functions.Execute("WebService_APIResult", e, res)\par
                Return ""\par
            End If \par
            itemDr("MissingQty") = missingQty\par
            ' \'c9\'d9\'bb\'f5\'ca\'d5\'bb\'f5\'a3\'ac\'b8\'b3\'d6\'b5\'d0\'c2\'cd\'d0\'c5\'cc\'ba\'c5\par
            If xData.Property("PalletNo") IsNot Nothing Then\par
                itemDr("PalletNo") = xData("PalletNo")\par
            End If \par
    End Select \par
    \par
    itemDr.Save()\par
    \par
    ' \'c9\'fa\'b3\'c9\'c8\'eb\'bf\'e2\'cd\'d0\'c5\'cc\'bc\'c7\'c2\'bc\par
    cmd.CommandText = "MERGE INTO Record4Pallet as Target USING (Select HeadID,HBL,PalletNo,SSCC,AreaID,AreaName,PackId From BWInOrderItem Where Del = 0 And InboundGRStatus = 0 And Guid = ?) As Source " & _\par
    "On Target.DocID = Source.HeadID And Target.SSCC = Source.SSCC And Target.PalletNo = Source.PalletNo And Target.Del = 0 " & _\par
    "WHEN NOT MATCHED THEN INSERT (Guid,DocID,HBL,SSCC,PalletNo,CreateDate,CreateBy,Del,PalletType,PackId,AreaID,AreaName) Values " & _\par
    "(NEWID(),Source.HeadID,Source.HBL,Source.SSCC,Source.PalletNo,GETDATE(),'" & userName & "',0,'I',Source.PackId,Source.AreaID,Source.AreaName);"\par
    cmd.Parameters.Add("@Guid", receivedID)\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear()\par
    \par
    ' \'b8\'fc\'d0\'c2\'cd\'d0\'c5\'cc\'bc\'c7\'c2\'bc\'b5\'c4ID\'b5\'bd\'b6\'a9\'b5\'a5\'c3\'f7\'cf\'b8\par
    cmd.CommandText = "Update BWInOrderItem Set PalletID = p.Guid, BIN = p.VBIN From BWInOrderItem i Inner Join Record4Pallet p On i.HeadID = p.DocID And i.SSCC = p.SSCC And i.PalletNo = p.PalletNo And p.Del = 0 Where i.Del = 0 And i.ReceivingStatus = 1"\par
    cmd.ExecuteNonQuery\par
    \par
    ' \'b2\'e5\'c8\'ebCSAR\'b4\'fd\'bc\'ec\'bc\'c7\'c2\'bc\par
    Dim csarID As String = Guid.NewGuid.ToString\par
    cmd.CommandText = "MERGE INTO Record4CSARQC as Target USING (Select HeadID, HBL, MatCode, ReceivedBatch From BWInOrderItem Where Del = 0 And ReceivingStatus = 1 And Guid = ?) As Source " & _\par
    "On Target.Del = 0 And Target.HeadID = Source.HeadID And Target.MatCode = Source.MatCode And Target.BatchNo = Source.ReceivedBatch " & _\par
    "WHEN NOT MATCHED THEN INSERT (Guid,HeadID,HBL,MatCode,BatchNo,Status,Del,CreateBy,CreateDate,ExpireDate,SPF,BarCode) Values " & _\par
    "(?,Source.HeadID,Source.HBL,Source.MatCode,Source.ReceivedBatch,0,0,?,GETDATE(),'NA','NA','NA');"\par
    cmd.Parameters.Add("@Guid", receivedID)\par
    cmd.Parameters.Add("@Guidcsar", csarID)\par
    cmd.Parameters.Add("@CreateBy", userName)\par
    If cmd.ExecuteNonQuery = 0 Then\par
        res("data") = ""\par
    Else\par
        res("data") = csarID\par
    End If \par
    cmd.Parameters.Clear()\par
    \par
    cmd.Commit()\par
Catch ex As Exception\par
    output.show(ex.tostring)\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d4\'d9\'ca\'d4!"\par
    cmd.Rollback()\par
End Try\par
\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWReceiving\\BWInOrderReceivingCheckBatch\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select FORMAT(ShelfLifeExpirationDate, 'yyyy-MM-dd') AS ShelfLifeExpirationDate From View_NEOBatchMasterWithSub Where Del = 0 And SubBatch = ?"\par
cmd.Parameters.Add("@SubBatch", xData("id").ToString)\par
Dim dt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
If dt.DataRows.Count = 0 Then\par
    res("error") = "\'c5\'fa\'b4\'ce\'d6\'f7\'ca\'fd\'be\'dd\'b2\'bb\'b4\'e6\'d4\'da\'a3\'ac\'c7\'eb\'c1\'f4\'d2\'e2\'ca\'b5\'ce\'ef\'c5\'fa\'b4\'ce\'a3\'ac\'ca\'d6\'b6\'af\'cc\'ee\'c8\'eb\'d0\'a7\'c6\'da\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
Else\par
    res("data") = dt.DataRows(0)("ShelfLifeExpirationDate").ToString\par
End If\par
\par
\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWReceiving\\BWInOrderReceivingGetList\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
\par
'\'ba\'cf\'b3\'c9\'b2\'e9\'d1\'af\'cc\'f5\'bc\'fe\par
Dim conditionStr As String = Functions.Execute("WebService_BuildConditionStr", e.path, xData)\par
\par
'\'b4\'a6\'c0\'edSSCC\par
If xData.Property("sscc") IsNot Nothing Then\par
    Dim sscc As String = xData("sscc").ToString\par
    If sscc.Length = 20 AndAlso sscc.StartsWith("00") Then\par
        '\'b5\'b1SSCC\'ce\'aa00\'bf\'aa\'cd\'b7+18\'ce\'bb\'d7\'d6\'b7\'fb\'a3\'ac\'b4\'a6\'c0\'edGS1-128\'bc\'e6\'c8\'dd\par
        conditionStr = conditionStr & " And SSCC18 = '" & sscc.Substring(2, 18) & "'"\par
    Else\par
        conditionStr = conditionStr & " And SSCC18 = '" & sscc & "'" \par
    End If\par
End If \par
\par
Dim resJo As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
'\'b0\'b4\'cc\'f5\'bc\'fe\'b2\'e9\'d1\'af\'ca\'fd\'be\'dd\'d7\'dc\'cc\'f5\'ca\'fd\par
cmd.CommandText = "Select Count(_Identify) As Total From BWInOrderItem Where Del = 0 And ReceivingStatus = 0 " & conditionStr\par
resJo("total") = CInt(cmd.ExecuteScalar())\par
\par
'\'b0\'b4\'cc\'f5\'bc\'fe\'b7\'d6\'d2\'b3\'b2\'e9\'d1\'af\par
cmd.CommandText = "SELECT Guid,PalletNo,EstimatedReceivedQty,MatCode,InboundBatch,iBarCode,iCName FROM BWInOrderItem WHERE Del = 0 And ReceivingStatus = 0 \{0\} ORDER BY \{1\} OFFSET \{2\} ROWS FETCH NEXT \{3\} ROWS ONLY"\par
cmd.CommandText = CExp(cmd.CommandText, conditionStr, xData("sort").ToString, (CInt(xData("currentPage").Tostring) - 1) * CInt(xData("pageSize").Tostring), CInt(xData("pageSize").Tostring))\par
Output.Show(cmd.CommandText)\par
Dim dt As DataTable = cmd.ExecuteReader\par
resJo("items") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
\par
\par
res("data") = resJo\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWInventoryTaking\\BWInventoryTakingGetData\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
cmd.CommandText = "Select * From View_StockTakingItemEx Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", xData("id").ToString)\par
Dim dt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
If dt.DataRows.Count = 0 Then\par
    res("error") = "\'c5\'cc\'b5\'e3\'c8\'ce\'ce\'f1\'b2\'bb\'b4\'e6\'d4\'da\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
Else\par
    res("data") = Functions.Execute("Sys_DataRow2JObject", dt.DataRows(0), False)\par
End If\par
\par
\par
cmd.CommandText = "Select IsBright From StockTakingHead Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", res("data")("HeadID").ToString)\par
Dim isBright As Boolean = cmd.ExecuteScalar\par
res("data")("IsBright") = isBright\par
\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWInventoryTaking\\BWInventoryTakingGetItem1st\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "View_StockTakingItemEx", "*", " And Del = 0 And TakingStatus = 0", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWInventoryTaking\\BWInventoryTakingGetItem2nd\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "View_StockTakingItemEx", "*", " And Del = 0 And TakingStatus = 1 And IsDiff = 1 And ReTakingStatus = 0", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWInventoryTaking\\BWInventoryTakingGetList\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As JObject = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "StockTakingHead", "Guid,TakingNo,OwnerShortName,WarehouseName,PreTakingDate,(Case When IsBright = 1 Then '\'c3\'f7\'c5\'cc' Else '\'c3\'a4\'c5\'cc' End) As TakingMethod", "And Del = 0 And IsClosed = 0", False)\par
\par
res("data") = resJo\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWInventoryTaking\\BWInventoryTakingItemUpdate\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
Dim res As New JObject\par
\par
Dim typ As String = xData("type").ToString\par
Dim id As String = xData("id").ToString\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
cmd.CommandText = "Select * From StockTakingItem Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", id)\par
Dim itemDt As DataTable = cmd.ExecuteReader(True)\par
\par
If itemDt.DataRows.Count = 0 Then\par
    res("error") = "\'c5\'cc\'b5\'e3\'c8\'ce\'ce\'f1\'d7\'b4\'cc\'ac\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'b7\'b5\'bb\'d8\'b2\'e9\'bf\'b4!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
\par
Dim dr As DataRow = itemDt.DataRows(0)\par
\par
Select Case typ\par
    Case "1st"\par
        If xData.Property("ActualQty") Is Nothing Then\par
            res("error") = "\'cc\'e1\'bd\'bb\'ca\'fd\'be\'dd\'d2\'ec\'b3\'a3!"\par
            Functions.Execute("WebService_APIResult", e, res)\par
            Return ""\par
        End If\par
        \par
        ' \'b4\'a6\'c0\'ed\'b3\'f5\'b4\'ce\'c5\'cc\'b5\'e3\'ca\'fd\'c1\'bf\'ba\'cd\'b2\'ee\'d2\'ec\'ca\'fd\'c1\'bf\par
        dr("TakingQty") = CInt(xData("ActualQty").ToString)\par
        dr("TakingDiffQty") = dr("TakingQty") - dr("StockQty")\par
        \par
        If dr("TakingDiffQty") = 0 Then\par
            ' \'c8\'e7\'b9\'fb\'ce\'de\'b2\'ee\'d2\'ec\'a3\'ac\'b1\'ea\'bc\'c7\'cd\'ea\'b3\'c9\par
            dr("IsDiff") = 0\par
            dr("ReTakingStatus") = 1\par
        Else\par
            ' \'c8\'e7\'b9\'fb\'d3\'d0\'b2\'ee\'d2\'ec\'a3\'ac\'b1\'ea\'bc\'c7\'b2\'ee\'d2\'ec\'ba\'cd\'b8\'b4\'c5\'cc\'d7\'b4\'cc\'ac\par
            dr("IsDiff") = 1\par
            dr("ReTakingStatus") = 0\par
        End If\par
        \par
        dr("TakingStatus") = 1\par
        dr("UpdateBy") = userName\par
        dr("UpdateDate") = Date.Now\par
        dr("TakingBy1ST") = userName\par
        dr("TakingDate1ST") = Date.Now\par
        \par
    Case "2nd"\par
        If xData.Property("UnrestrictedQty") Is Nothing OrElse xData.Property("FrozenQty") Is Nothing Then\par
            res("error") = "\'cc\'e1\'bd\'bb\'ca\'fd\'be\'dd\'d2\'ec\'b3\'a3!"\par
            Functions.Execute("WebService_APIResult", e, res)\par
            Return ""\par
        End If\par
        \par
        ' \'b4\'a6\'c0\'ed\'b8\'b4\'c5\'cc\'ca\'fd\'c1\'bf\par
        dr("ActualUnrestrictedQty") = CInt(xData("UnrestrictedQty").ToString)\par
        dr("DiffUnrestricted") = dr("ActualUnrestrictedQty") - dr("UnrestrictedQty")\par
        dr("ActualBlockedQty") = CInt(xData("FrozenQty").ToString)\par
        dr("DiffBlocked") = dr("ActualBlockedQty") - dr("BlockedQty")\par
        \par
        dr("ReTakingStatus") = 1\par
        dr("UpdateBy") = userName\par
        dr("UpdateDate") = Date.Now\par
        dr("TakingBy2st") = userName\par
        dr("TakingDate2st") = Date.Now\par
End Select\par
\par
dr.Save()\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutDispatch\\BWOutDispatchCreateDoc\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
\par
Dim dispatchDate As Date = CDate(xData("date").ToString)\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
Try\par
    cmd.BeginTransaction()\par
    \par
    Dim prefixDr As DataRow = DataTables("InternalParameters").Find("ParameterType = 'DocPrefix' And ParameterName = 'Dispatch'")\par
    If prefixDr Is Nothing Then\par
        res("error") = "\'c5\'c9\'b3\'b5\'b5\'a5\'c7\'b0\'d7\'ba\'ce\'b4\'c5\'e4\'d6\'c3\'a3\'ac\'c7\'eb\'c1\'aa\'cf\'b5\'b9\'dc\'c0\'ed\'d4\'b1\'a3\'a1"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    Dim prefix As String = prefixDr("ParameterValue") & Format(dispatchDate, "yyyyMMdd")\par
    Dim suffixLength As Integer = Val(prefixDr("ParameterValueExt"))\par
    If suffixLength = 0 Then\par
        suffixLength = 4\par
    End If\par
\par
    ' \'bb\'f1\'c8\'a1\'d0\'c2\'b5\'c4\'c5\'c9\'b3\'b5\'b5\'a5\'ba\'c5\par
    cmd.CommandText = "Select Right(Max(DispatchNo)," & suffixLength & ") From BWOutDispatchHead Where Del = 0 And DispatchNo Like '" & prefix & "%'"\par
    Dim maxNo As String = cmd.ExecuteScalar\par
    Dim maxNum As Integer = 1\par
    If isnumeric(maxNo) Then\par
        maxNum = CInt(maxNo) + 1\par
    End If\par
    \par
    Dim doc As String = prefix & maxNum.ToString.PadLeft(suffixLength, "0")\par
    Output.Show(doc)\par
    \par
    ' \'b4\'b4\'bd\'a8\'c5\'c9\'b3\'b5\'b5\'a5\par
    Dim orderId As String = Guid.NewGuid.ToString\par
    cmd.CommandText = "Insert Into BWOutDispatchHead (CreateDate,CreateBy,Guid,DispatchNo,Del,DeliveryStatus,AddressFrom) Values (GETDATE(),?,?,?,0,0,'TC')"\par
    cmd.Parameters.Add("@CreateBy", userName)\par
    cmd.Parameters.Add("@Guid", orderId)\par
    cmd.Parameters.Add("@DispatchNo", doc)\par
    If cmd.ExecuteNonQuery = 1 Then\par
        res("data") = orderId\par
    Else\par
        res("error") = "\'c5\'c9\'b3\'b5\'b5\'a5\'b4\'b4\'bd\'a8\'ca\'a7\'b0\'dc!"\par
    End If\par
    cmd.Parameters.Clear()\par
    \par
    cmd.Commit()\par
Catch ex As Exception\par
    output.show(ex.tostring)\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d4\'d9\'ca\'d4!"\par
    cmd.Rollback()\par
End Try\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutDispatch\\BWOutDispatchGetDetail\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As New JObject\par
res("data") = resJo\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select DispatchNo,AddressTo,LicensePlate,TruckModel From BWOutDispatchHead Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", xData("id").ToString)\par
Dim dt As DataTable = cmd.ExecuteReader\par
If dt.DataRows.Count = 1 Then\par
    resJo("head") = Functions.Execute("Sys_DataRow2JObject", dt.DataRows(0), True)\par
Else\par
    res("error") = "\'c8\'eb\'bf\'e2\'b6\'a9\'b5\'a5\'bb\'f1\'c8\'a1\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d4\'d9\'ca\'d4\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
cmd.Parameters.Clear\par
\par
Dim optionJa As New JArray\par
resJo("options") = optionJa\par
' \'b4\'a6\'c0\'ed\'d7\'d6\'b5\'e4\'d1\'a1\'cf\'ee\par
cmd.CommandText = "Select DictionaryType,DictionaryName,DictionaryValue From BaseDictionary Where Del = 0 And IsActived = 1 And DictionaryType In ('AddressTo','TruckModel') Order By SortBy,DictionaryName"\par
Dim optionDt As DataTable = cmd.ExecuteReader\par
For i As Integer = 0 To optionDt.DataRows.Count - 1\par
    optionJa.Add(New JObject)\par
    optionJa(i)("type") = optionDt.DataRows(i)("DictionaryType").Tostring\par
    optionJa(i)("label") = optionDt.DataRows(i)("DictionaryName").Tostring\par
    optionJa(i)("value") = optionDt.DataRows(i)("DictionaryValue").Tostring \par
Next\par
\par
\par
cmd.CommandText = "Select d.*,p.NewSSCC,p.NewPalletNo From BWOutDispatchItem d Left Join BWOutPickingItem p On d.PickingItemID = p.Guid Where d.Del = 0 And d.HeadID = ?"\par
cmd.Parameters.Add("@HeadID", xData("id").ToString)\par
Dim itemDt As DataTable = cmd.ExecuteReader()\par
cmd.Parameters.Clear()\par
resJo("items") = Functions.Execute("Sys_DataTable2Json", itemDt, False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutDispatch\\BWOutDispatchGetUnFinishDoc\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As JObject = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "View_BWOutDispatchHeadEx", "*", "And Del = 0 And DeliveryStatus = 0", False)\par
\par
res("data") = resJo\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutDispatch\\BWOutDispatchHeadUpdate\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
cmd.CommandText = "Update BWOutDispatchHead Set AddressTo = ?, LicensePlate = ?, TruckModel = ? Where Del = 0 And DeliveryStatus = 0 And Guid = ?"\par
cmd.Parameters.Add("@AddressTo", xData.Value(Of String)("AddressTo"))\par
cmd.Parameters.Add("@LicensePlate", xData.Value(Of String)("LicensePlate"))\par
cmd.Parameters.Add("@TruckModel", xData.Value(Of String)("TruckModel"))\par
cmd.Parameters.Add("@Guid", xData("id").ToString)\par
If cmd.ExecuteNonQuery = 0 Then\par
    res("error") = "\'b1\'ed\'cd\'b7\'b8\'fc\'d0\'c2\'ca\'a7\'b0\'dc\'a3\'a1"\par
End If \par
cmd.Parameters.Clear\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutDispatch\\BWOutDispatchPalletLoad\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim headID As String = xData("dispatchId").ToString\par
\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
Try\par
    cmd.BeginTransaction()\par
    \par
    cmd.CommandText = "Select DispatchNo,DeliveryStatus From BWOutDispatchHead Where Del = 0 And Guid = '" & headID & "'"\par
    Dim Values = cmd.ExecuteValues\par
    If Values.Count = 0 Then\par
        res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d6\'d8\'ca\'d4\'a3\'a1"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    ' \'d0\'a3\'d1\'e9\'c5\'c9\'b3\'b5\'b5\'a5\'ca\'c7\'b7\'f1\'b7\'a2\'bb\'f5\par
    If Values("DeliveryStatus") Then\par
        res("error") = "\'c5\'c9\'b3\'b5\'b5\'a5\'d2\'d1\'b7\'a2\'bb\'f5\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    ' \'bb\'f1\'c8\'a1\'cd\'d0\'c5\'cc\'d0\'c5\'cf\'a2\par
    cmd.CommandText = "Select Guid From Record4Pallet Where Del = 0 And PalletType = 'O' And SSCC = ?"\par
    cmd.Parameters.Add("@SSCC", xData("sscc").ToString)\par
    Dim palletID As String = cmd.ExecuteScalar\par
    cmd.Parameters.Clear()\par
    If palletID = "" Then\par
        res("error") = "\'cc\'e1\'bd\'bb\'b5\'c4SSCC\'b2\'bb\'b4\'e6\'d4\'da\'a3\'a1"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
\par
    cmd.CommandText = "Select _Identify From BWOutPickingItem Where Del = 0 And PackagingStatus = 1 And LoadingStatus = 0 And NewPalletID = ?"\par
    cmd.Parameters.Add("@NewPalletID", palletID)\par
    If cmd.ExecuteScalar Is Nothing Then\par
        res("error") = "\'cc\'e1\'bd\'bb\'b5\'c4SSC\'ce\'de\'b7\'a8\'d7\'b0\'d4\'d8\'a3\'a1"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    cmd.Parameters.Clear()\par
    \par
    ' \'b4\'b4\'bd\'a8DispatchItem\'a3\'ac\'d5\'fd\'b3\'a3\'b2\'fa\'c6\'b7\par
    Dim opid As String = Guid.NewGuid.ToString\par
    cmd.CommandText = "Insert Into BWOutDispatchItem (Guid,HeadID,PickingItemID,StockID,StockParentID,SSCC,PalletID,DispatchNo,Del,CreateDate,CreateBy,OPID,GoodsNum,DeliveryGoodsType,MatCode,BatchNo) " & _\par
    "Select NEWID(),?,Guid,NEWID(),StockID,NewSSCC,NewPalletID,?,0,GETDATE(),?,?,(PickingQty - CIQQty - ShelveQty),(CASE WHEN PickingType = 'Sample' THEN '\'c1\'f4\'d1\'f9' ELSE '\'d5\'fd\'b3\'a3' End),MatCode,BatchNo From BWOutPickingItem Where Del = 0 And PackagingStatus = 1 And LoadingStatus = 0 And (PickingQty - CIQQty - ShelveQty > 0) And NewPalletID = ?"\par
    cmd.Parameters.Add("@HeadID", headID)\par
    cmd.Parameters.Add("@DispatchNo", Values("DispatchNo"))\par
    cmd.Parameters.Add("@CreateBy", userName)\par
    cmd.Parameters.Add("@OPID", opid)\par
    cmd.Parameters.Add("@NewPalletID", palletID)\par
    cmd.ExecuteNonQuery\par
    \par
    ' \'b4\'b4\'bd\'a8DispatchItem\'a3\'acCIQ\'b2\'fa\'c6\'b7\par
    cmd.CommandText = "Insert Into BWOutDispatchItem (Guid,HeadID,PickingItemID,StockID,StockParentID,SSCC,PalletID,DispatchNo,Del,CreateDate,CreateBy,OPID,GoodsNum,DeliveryGoodsType,MatCode,BatchNo) " & _\par
    "Select NEWID(),?,Guid,NEWID(),StockID,'9TCCIQ' + RIGHT(NewSSCC,12),NewPalletID,?,0,GETDATE(),?,?,CIQQty,'CIQ',MatCode,BatchNo From BWOutPickingItem Where Del = 0 And PackagingStatus = 1 And LoadingStatus = 0 And CIQQty > 0 And NewPalletID = ?"\par
    cmd.ExecuteNonQuery\par
    \par
    ' \'b4\'b4\'bd\'a8DispatchItem\'a3\'ac\'b8\'e9\'d6\'c3\'b2\'fa\'c6\'b7\par
    cmd.CommandText = "Insert Into BWOutDispatchItem (Guid,HeadID,PickingItemID,StockID,StockParentID,SSCC,PalletID,DispatchNo,Del,CreateDate,CreateBy,OPID,GoodsNum,DeliveryGoodsType,MatCode,BatchNo) " & _\par
    "Select NEWID(),?,Guid,NEWID(),StockID,'9TCL97' + RIGHT(NewSSCC,12),NewPalletID,?,0,GETDATE(),?,?,ShelveQty,'\'b8\'e9\'d6\'c3',MatCode,BatchNo From BWOutPickingItem Where Del = 0 And PackagingStatus = 1 And LoadingStatus = 0 And ShelveQty > 0 And NewPalletID = ?"\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear()\par
    \par
    ' \'b4\'a6\'c0\'ed\'bc\'f0\'bb\'f5\'b4\'f2\'b0\'fc\'bf\'e2\'b4\'e6\par
    cmd.CommandText = "Update StockDetail Set StockQty = StockQty - p.PickingQty, UnrestrictedQty = UnrestrictedQty - p.PickingQty From StockDetail s Inner Join BWOutPickingItem p " & _\par
    "On s.Guid = p.StockID Where s.Del = 0 And s.IsWorking = 0 And p.Del = 0 And p.PackagingStatus = 1 And p.LoadingStatus = 0 And p.NewPalletID = ?"\par
    cmd.Parameters.Add("@NewPalletID", palletID)\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear()\par
\par
    ' \'d0\'c2\'d4\'f6\'d7\'b0\'b3\'b5\'b7\'a2\'bb\'f5\'bf\'e2\'b4\'e6\par
    cmd.CommandText = "Insert Into StockDetail (Guid,ParentID,StockStatus,WarehouseCode,OwnerCode,Plant,StorageLocation,MatCode,BatchNo,SAPBatch,ActualExpiration,AreaID,AreaName,PalletID,PalletNo,SSCC,StockQty,BlockedQty,UnrestrictedQty,InDetailID,InHeadID,HBL,Invoice,OutHeadID,BLNo,TallyDetailID,DocNo,CreateBy,CreateDate,Del,LockedByMovement,LockedByTaking,LockedByAPI,IsWorking) " & _\par
    "Select d.StockID,d.StockParentID,'OutDispatch',s.WarehouseCode,s.OwnerCode,s.Plant,s.StorageLocation,s.MatCode,s.BatchNo,s.SAPBatch,s.ActualExpiration,s.AreaID,s.AreaName,s.PalletID,s.PalletNo,d.SSCC,d.GoodsNum,0,d.GoodsNum,s.InDetailID,s.InHeadID,s.HBL,s.Invoice,s.OutHeadID,s.BLNo,s.TallyDetailID,d.DispatchNo,?,GETDATE(),0,0,0,0,1 From BWOutDispatchItem d " & _\par
    "Left Join StockDetail s On d.StockParentID = s.Guid Where d.OPID = ?"\par
    cmd.Parameters.Add("@CreateBy", userName)\par
    cmd.Parameters.Add("@OPID", opid)\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear()\par
    \par
    ' \'b1\'ea\'bc\'c7\'d7\'b0\'b3\'b5\par
    cmd.CommandText = "Update BWOutPickingItem Set LoadingStatus = 1, LoadingBy = ?, LoadingDate = GetDate(), LoadingDispatch = ?, LoadingDispatchID = ? Where Del = 0 And PackagingStatus = 1 And LoadingStatus = 0 And NewPalletID = ?"\par
    cmd.Parameters.Add("@LoadingBy", userName)\par
    cmd.Parameters.Add("@LoadingDispatch", Values("DispatchNo"))\par
    cmd.Parameters.Add("@LoadingDispatchID", headID)\par
    cmd.Parameters.Add("@NewPalletID", palletID)\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear()\par
   \par
    cmd.Commit()\par
Catch ex As Exception\par
    output.show(ex.tostring)\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d4\'d9\'ca\'d4!"\par
    cmd.Rollback()\par
End Try\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutDispatch\\BWOutDispatchPalletRemove\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim headID As String = xData("dispatchId").ToString\par
dim palletID As String = xData("palletId").ToString\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
Try\par
    cmd.BeginTransaction()\par
    \par
    cmd.CommandText = "Select DispatchNo,DeliveryStatus From BWOutDispatchHead Where Del = 0 And Guid = '" & headID & "'"\par
    Dim Values = cmd.ExecuteValues\par
    If Values.Count = 0 Then\par
        res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d6\'d8\'ca\'d4\'a3\'a1"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    ' \'d0\'a3\'d1\'e9\'c5\'c9\'b3\'b5\'b5\'a5\'ca\'c7\'b7\'f1\'b7\'a2\'bb\'f5\par
    If Values("DeliveryStatus") Then\par
        res("error") = "\'c5\'c9\'b3\'b5\'b5\'a5\'d2\'d1\'b7\'a2\'bb\'f5\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    ' \'b8\'fc\'d0\'c2\'bc\'f0\'bb\'f5\'b5\'a5\'c3\'f7\'cf\'b8\'d7\'b0\'b3\'b5\'d7\'b4\'cc\'ac\par
    cmd.CommandText = "Update BWOutPickingItem Set LoadingStatus = 0, LoadingBy = Null, LoadingDate = Null, LoadingDispatch = Null, LoadingDispatchID = Null, UpdateDate = GETDATE(), UpdateBy = ? Where Del = 0 And LoadingStatus = 1 And NewPalletID = ?"\par
    cmd.Parameters.Add("@UpdateBy", userName)\par
    cmd.Parameters.Add("@NewPalletID", palletID)\par
    cmd.ExecuteNonQuery\par
    \par
    ' \'b4\'a6\'c0\'ed\'d7\'b0\'b3\'b5\'bf\'e2\'b4\'e6\par
    cmd.CommandText = "Update StockDetail Set Del = 1, UpdateDate = GETDATE(), UpdateBy = ? Where Del = 0 And StockStatus = 'OutDispatch' And PalletID = ?"\par
    cmd.ExecuteNonQuery\par
    \par
    ' \'b4\'a6\'c0\'ed\'bc\'f0\'bb\'f5\'bf\'e2\'b4\'e6\par
    cmd.CommandText = "Update StockDetail Set StockQty = StockQty + p.PickingQty, UnrestrictedQty = UnrestrictedQty + p.PickingQty, UpdateDate = GETDATE(), UpdateBy = ? From StockDetail s Inner Join BWOutPickingItem p On s.Guid = p.StockID Where s.Del = 0 And s.StockStatus = 'OutPicking' And p.Del = 0 And p.LoadingStatus = 0 And p.NewPalletID = ?"\par
    cmd.ExecuteNonQuery\par
    \par
    ' \'b1\'ea\'bc\'c7\'c9\'be\'b3\'fd\'d7\'b0\'b3\'b5\'c3\'f7\'cf\'b8\par
    cmd.CommandText = "Update BWOutDispatchItem Set Del = 1, UpdateDate = GETDATE(), UpdateBy = ? Where Del = 0 And PalletID = ?"\par
    cmd.ExecuteNonQuery\par
    \par
    cmd.Commit()\par
Catch ex As Exception\par
    output.show(ex.tostring)\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d4\'d9\'ca\'d4!"\par
    cmd.Rollback()\par
End Try\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutLabing\\BWOutOrderGetUnLabingBL\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "View_MB_BWOutOrderUnProcessing", "*", "", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutTally\\BWOutOrderGetUnTallyBL\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "View_MB_BWOutOrderUnTalllyBL", "*", "", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutTally\\BWOutOrderItemTally\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
Dim id As String = xData("id").ToString\par
Dim isBatchDiff As Boolean = CBool(xData("IsBatchDiff"))\par
Dim tallyQty As Integer = Val(xData("TallyQty"))\par
Dim tallyBatch As String = xData.Value(Of String)("TallyBatch")\par
tallyBatch = tallyBatch.Trim\par
Dim tallyExpiration As String = xData.Value(Of String)("TallyExpiration")\par
tallyExpiration = tallyExpiration.Trim\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
Try\par
    cmd.BeginTransaction()\par
    \par
    ' \'bb\'f1\'c8\'a1\'c0\'ed\'bb\'f5\'c3\'f7\'cf\'b8\par
    cmd.CommandText = "Select * From BWOutTally Where Del = 0 And TallyStatus = 0 And Guid = ?"\par
    cmd.Parameters.Add("@Guid", id)\par
    Dim itemDt As DataTable = cmd.ExecuteReader(True)\par
    cmd.Parameters.Clear()\par
    If itemDt.DataRows.Count = 0 Then\par
        res("error") = "\'c3\'f7\'cf\'b8\'d7\'b4\'cc\'ac\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    Dim itemDr As DataRow = itemDt.DataRows(0)\par
    \par
    ' \'bb\'f1\'c8\'a1\'bf\'e2\'b4\'e6\'c3\'f7\'cf\'b8\par
    cmd.CommandText = "Select * From StockDetail Where Del = 0 And IsWorking = 1 And Guid = ?"\par
    cmd.Parameters.Add("@Guid", itemDr("StockID"))\par
    Dim stockDt As DataTable = cmd.ExecuteReader(True)\par
    cmd.Parameters.Clear()\par
    If stockDt.DataRows.Count = 0 Then\par
        res("error") = "\'bf\'e2\'b4\'e6\'c3\'f7\'cf\'b8\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'d6\'d8\'ca\'d4!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    Dim stockDr As DataRow = stockDt.DataRows(0)\par
    \par
    ' \'d0\'a3\'d1\'e9\'bf\'e2\'b4\'e6\'d7\'b4\'cc\'ac\par
    If stockDr("LockedByMovement") OrElse stockDr("LockedByTaking") OrElse stockDr("LockedByAPI") Then\par
        res("error") = "\'bf\'e2\'b4\'e6\'b4\'a6\'d3\'da\'cb\'f8\'b6\'a8\'d7\'b4\'cc\'ac\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    cmd.CommandText = "Select isBatchManagementRequired From NEOProductMaster Where Del = 0 And skuCode = ? "\par
    cmd.Parameters.Add("@Material", itemDr("MatCode"))\par
    Dim isBatchManagementRequired As Boolean = cmd.ExecuteScalar\par
    cmd.Parameters.Clear\par
    Dim tallyMasterBatch As String\par
    If isBatchManagementRequired Then\par
        \par
        ' \'bb\'f1\'c8\'a1\'c0\'ed\'bb\'f5\'c5\'fa\'b4\'ce\'d0\'c5\'cf\'a2\par
        cmd.CommandText = "Select ISNULL(MasterBatch,'') As MasterBatch, ISNULL(MatlBatchIsInRstrcdUseStock,-1) As BatchStatus From NEOBatchMapping Where Del = 0 And Material = ? And SubBatch = ?"\par
        cmd.Parameters.Add("@Material", itemDr("MatCode"))\par
        cmd.Parameters.Add("@SubBatch", tallyBatch)\par
        Dim batchValuse = cmd.ExecuteValues\par
        cmd.Parameters.Clear\par
        \par
        ' \'d0\'a3\'d1\'e9\'c5\'fa\'b4\'ce\'d7\'b4\'cc\'ac\par
        If batchValuse.Count = 0 Then\par
            res("error") = "\'c0\'ed\'bb\'f5\'c5\'fa\'b4\'ce\'b2\'bb\'b4\'e6\'d4\'da\'a3\'ac\'ce\'de\'b7\'a8\'b7\'a2\'bb\'f5\'a3\'ac\'c7\'eb\'b9\'b5\'cd\'a8\'b4\'a6\'c0\'ed\'b7\'bd\'b0\'b8!"\par
            Functions.Execute("WebService_APIResult", e, res)\par
            Return ""\par
        End If\par
        \par
        tallyMasterBatch = batchValuse("MasterBatch")\par
        If batchValuse("BatchStatus") <> 0 Then\par
            res("error") = "\'c0\'ed\'bb\'f5\'c5\'fa\'b4\'ce\'d7\'b4\'cc\'ac\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'bc\'ec\'b2\'e9\'a3\'a1"\par
            Functions.Execute("WebService_APIResult", e, res)\par
            Return ""\par
        End If\par
    Else\par
        tallyMasterBatch = ""\par
    End If\par
    \par
    itemDr("UpdateBy") = userName\par
    itemDr("UpdateDate") = Date.Now\par
    \par
    If isBatchDiff = False OrElse (isBatchDiff AndAlso tallyQty = itemDr("GoodsNum")) Then\par
        ' \'c5\'fa\'b4\'ce\'ce\'de\'b2\'ee\'d2\'ec\'a3\'ac\'bb\'f2\'d5\'df\'c8\'ab\'b2\'bf\'ca\'fd\'c1\'bf\'b5\'c4\'c5\'fa\'b4\'ce\'b2\'ee\'d2\'ec\'a3\'ac\'d6\'b1\'bd\'d3\'b8\'fc\'d0\'c2\'c0\'ed\'bb\'f5\'d0\'c5\'cf\'a2\par
        itemDr("TallyStatus") = 1\par
        itemDr("TallyBy") = userName\par
        itemDr("TallyDate") = Date.Now\par
        itemDr("IsBatchDiff") = isBatchDiff\par
        itemDr("RetentionQty") = Val(xData("RetentionQty"))\par
        itemDr("CIQQty") = Val(xData("CIQQty"))\par
        itemDr("MissingQty") = Val(xData("MissingQty"))\par
        itemDr("TallyBoxNum") = Val(xData("TallyBoxNum"))\par
        itemDr("TallyQty") = tallyQty\par
        itemDr("ShelveQty") = Val(xData("ShelveQty"))\par
        \par
        ' \'d0\'a3\'d1\'e9\'b8\'e9\'d6\'c3\'ca\'fd\'c1\'bf\par
        If itemDr("ShelveQty") > itemDr("TallyQty") Then\par
            res("error") = "\'b8\'e9\'d6\'c3\'ca\'fd\'c1\'bf\'b2\'bb\'c4\'dc\'b4\'f3\'d3\'da\'ca\'b5\'ce\'ef\'ca\'fd\'c1\'bf\'a3\'a1"\par
            Functions.Execute("WebService_APIResult", e, res)\par
            Return ""\par
        End If\par
        \par
        ' \'d0\'a3\'d1\'e9\'b8\'e9\'d6\'c3\'d4\'ad\'d2\'f2\par
        If itemDr("ShelveQty") > 0 Then\par
            If xData("ShelveReason").Type <> JTokenType.Null Then\par
                itemDr("ShelveReason") = xData("ShelveReason").ToString\par
            Else\par
                res("error") = "\'b8\'e9\'d6\'c3\'b2\'fa\'c6\'b7\'b1\'d8\'d0\'eb\'ca\'e4\'c8\'eb\'b8\'e9\'d6\'c3\'d4\'ad\'d2\'f2\'a3\'a1"\par
                Functions.Execute("WebService_APIResult", e, res)\par
                Return ""\par
            End If \par
        End If\par
        \par
        ' \'d0\'a3\'d1\'e9\'b2\'fa\'c6\'b7\'ca\'fd\'c1\'bf\par
        If itemDr("GoodsNum") <> itemDr("RetentionQty") + itemDr("CIQQty") + itemDr("MissingQty") + itemDr("TallyQty") Then\par
            res("error") = "\'c5\'fa\'b4\'ce\'ce\'de\'b2\'ee\'d2\'ec\'ca\'b1\'a3\'ac\'c1\'f4\'d1\'f9+CIQ+\'cc\'f9\'b1\'ea\'c9\'d9+\'ca\'b5\'ce\'ef\'d3\'a6\'b5\'c8\'d3\'da\'b2\'fa\'c6\'b7\'ca\'fd\'c1\'bf\'a3\'a1"\par
            Functions.Execute("WebService_APIResult", e, res)\par
            Return ""\par
        End If\par
        \par
        ' \'b4\'a6\'c0\'ed\'ca\'b5\'ce\'ef\'c5\'fa\'b4\'ce\'a3\'ac\'d0\'a7\'c6\'da\par
        itemDr("TallyBatch") = tallyBatch\par
        itemDr("TallyExpiration") = tallyExpiration\par
        \par
        ' \'cc\'d8\'ca\'e2\'b4\'a6\'c0\'ed\par
        itemDr("CIQRemaining") = itemDr("CIQQty")\par
        itemDr("MissingRemaining") = itemDr("MissingQty")\par
        itemDr("ShelveRemaining") = itemDr("ShelveQty")\par
        \par
        ' \'b4\'a6\'c0\'ed\'c1\'f4\'d1\'f9\'bf\'e2\'b4\'e6\par
        If itemDr("RetentionQty") > 0 Then\par
            Dim retStockDr As DataRow = stockDr.Clone\par
            retStockDr("Guid") = Guid.NewGuid.tostring\par
            retStockDr("ParentID") = stockDr("Guid")\par
            retStockDr("StockStatus") = "Sample"\par
            retStockDr("StockQty") = itemDr("RetentionQty")\par
            retStockDr("BlockedQty") = 0\par
            retStockDr("UnrestrictedQty") = itemDr("RetentionQty")\par
            retStockDr("CreateBy") = userName\par
            retStockDr("CreateDate") = Date.Now\par
            retStockDr("IsWorking") = 1\par
            \par
            ' \'b8\'fc\'d0\'c2\'c1\'f4\'d1\'f9\'d6\'b8\'c1\'ee\par
            cmd.CommandText = "Update Record4SampleCommand Set RecordedQty = RecordedQty + ?, SamplesRemainingQty = SamplesRemainingQty -? Where Del = 0 And HBL = ? And Invoice = ? And MatCode = ? And BatchNo = ?"\par
            cmd.Parameters.Add("@RecordedQty", itemDr("RetentionQty"))\par
            cmd.Parameters.Add("@SamplesRemainingQty", itemDr("RetentionQty"))\par
            cmd.Parameters.Add("@HBL", retStockDr("HBL"))\par
            cmd.Parameters.Add("@Invoice", retStockDr("Invoice"))\par
            cmd.Parameters.Add("@MatCode", retStockDr("MatCode"))\par
            cmd.Parameters.Add("@BatchNo", retStockDr("BatchNo"))\par
            cmd.ExecuteNonQuery\par
            cmd.Parameters.Clear\par
        End If\par
        \par
        ' \'b4\'a6\'c0\'ed\'ca\'b5\'ce\'ef\'bf\'e2\'b4\'e6\'a3\'ac\'bc\'f5\'c8\'a5\'c1\'f4\'d1\'f9\'b2\'bf\'b7\'d6\par
        stockDr("StockQty") = stockDr("StockQty") - itemDr("RetentionQty")\par
        stockDr("UnrestrictedQty") = stockDr("UnrestrictedQty") - itemDr("RetentionQty")\par
    Else\par
        ' \'c5\'fa\'b4\'ce\'d3\'d0\'b2\'ee\'d2\'ec\'a3\'ac\'b2\'f0\'b7\'d6\'b4\'a6\'c0\'ed\par
        If tallyQty = 0 Or tallyQty > itemDr("GoodsNum") Then\par
            res("error") = "\'c5\'fa\'b4\'ce\'d3\'d0\'b2\'ee\'d2\'ec\'ca\'b1\'a3\'ac\'ca\'b5\'ce\'ef\'d3\'a6\'b4\'f3\'d3\'da0\'c7\'d2\'b2\'bb\'c4\'dc\'b3\'ac\'b3\'f6\'b2\'fa\'c6\'b7\'ca\'fd\'c1\'bf\'a3\'a1"\par
            Functions.Execute("WebService_APIResult", e, res)\par
            Return ""\par
        End If\par
        \par
        ' \'b1\'ea\'bc\'c7\'c9\'be\'b3\'fd\'d4\'ad\'ca\'bcItem\par
        itemDr("Del") = 1\par
        \par
        ' \'b4\'a6\'c0\'ed\'b2\'f0\'b7\'d6\'bf\'e2\'b4\'e6\par
        Dim splitStockDr As DataRow = stockDr.Clone\par
        splitStockDr("Guid") = Guid.NewGuid.ToString()\par
        splitStockDr("ParentID") = stockDr("Guid")\par
        splitStockDr("StockQty") = stockDr("StockQty") - tallyQty\par
        splitStockDr("BlockedQty") = 0\par
        splitStockDr("UnrestrictedQty") = stockDr("StockQty") - tallyQty\par
        splitStockDr("CreateBy") = userName\par
        splitStockDr("CreateDate") = Date.Now\par
        \par
        ' \'d2\'d4\'b2\'fa\'c6\'b7\'ca\'fd\'c1\'bf-\'ca\'b5\'ce\'ef\'ca\'fd\'c1\'bf\'a3\'ac\'b4\'b4\'bd\'a8\'b2\'f0\'b7\'d6Item\'a3\'ac\'b2\'bb\'d7\'f6\'c0\'ed\'bb\'f5\'b4\'a6\'c0\'ed\par
        Dim splitDr As DataRow = itemDr.Clone\par
        splitDr("Guid") = Guid.NewGuid.ToString()\par
        splitDr("StockID") = splitStockDr("Guid")\par
        splitDr("StockParentID") = splitStockDr("ParentID")\par
        splitDr("Del") = 0\par
        splitDr("TallyStatus") = 0\par
        splitDr("SplitLevel") = itemDr("SplitLevel") + 1\par
        splitDr("GoodsNum") = itemDr("GoodsNum") - tallyQty\par
        \par
        ' \'b2\'f0\'b7\'d6\'cf\'ee\'bc\'cc\'b3\'d0\'bc\'d3\'b9\'a4\'c1\'f4\'d1\'f9\par
        splitDr("RetentionQty") = Val(xData("rt"))\par
        \par
        ' \'d2\'d4\'ca\'b5\'ce\'ef\'ca\'fd\'c1\'bf\'b2\'f0\'b7\'d6Item\'a3\'ac\'b2\'a2\'b4\'a6\'c0\'ed\'b2\'ee\'d2\'ec\'c5\'fa\'b4\'ce\par
        Dim newID = Guid.NewGuid.ToString()\par
        Dim tallyDr As DataRow = itemDr.Clone\par
        tallyDr("Guid") = newID\par
        tallyDr("Del") = 0\par
        tallyDr("TallyBy") = userName\par
        tallyDr("TallyDate") = Date.Now\par
        tallyDr("TallyStatus") = 1\par
        tallyDr("IsBatchDiff") = 1\par
        tallyDr("RetentionQty") = 0\par
        tallyDr("CIQQty") = 0\par
        tallyDr("MissingQty") = 0\par
        tallyDr("SplitLevel") = itemDr("SplitLevel") + 1\par
        tallyDr("GoodsNum") = tallyQty\par
        tallyDr("TallyQty") = tallyQty\par
        tallyDr("TallyBoxNum") = Val(xData("TallyBoxNum"))\par
        tallyDr("ShelveQty") = Val(xData("ShelveQty"))\par
        \par
        ' \'d0\'a3\'d1\'e9\'b8\'e9\'d6\'c3\'ca\'fd\'c1\'bf\par
        If tallyDr("ShelveQty") > tallyDr("TallyQty") Then\par
            res("error") = "\'b8\'e9\'d6\'c3\'ca\'fd\'c1\'bf\'b2\'bb\'c4\'dc\'b4\'f3\'d3\'da\'ca\'b5\'ce\'ef\'ca\'fd\'c1\'bf\'a3\'a1"\par
            Functions.Execute("WebService_APIResult", e, res)\par
            Return ""\par
        End If\par
        \par
        ' \'d0\'a3\'d1\'e9\'b8\'e9\'d6\'c3\'d4\'ad\'d2\'f2\par
        If tallyDr("ShelveQty") > 0 Then\par
            If xData("ShelveReason").Type <> JTokenType.Null Then\par
                tallyDr("ShelveReason") = xData("ShelveReason").ToString\par
            Else\par
                res("error") = "\'b8\'e9\'d6\'c3\'b2\'fa\'c6\'b7\'b1\'d8\'d0\'eb\'ca\'e4\'c8\'eb\'b8\'e9\'d6\'c3\'d4\'ad\'d2\'f2\'a3\'a1"\par
                Functions.Execute("WebService_APIResult", e, res)\par
                Return ""\par
            End If \par
        End If\par
        \par
        ' \'b4\'a6\'c0\'ed\'ca\'b5\'ce\'ef\'c5\'fa\'b4\'ce\'a3\'ac\'d0\'a7\'c6\'da\par
        tallyDr("TallyBatch") = tallyBatch\par
        tallyDr("TallyExpiration") = tallyExpiration\par
        \par
        ' \'cc\'d8\'ca\'e2\'b4\'a6\'c0\'ed\par
        tallyDr("CIQRemaining") = tallyDr("CIQQty")\par
        tallyDr("MissingRemaining") = tallyDr("MissingQty")\par
        tallyDr("ShelveRemaining") = tallyDr("ShelveQty")\par
        \par
        ' \'b4\'a6\'c0\'ed\'ca\'b5\'ce\'ef\'bf\'e2\'b4\'e6\par
        stockDr("StockQty") = tallyQty\par
        stockDr("UnrestrictedQty") = tallyQty\par
        stockDr("TallyDetailID") = tallyDr("Guid")\par
        splitStockDr("TallyDetailID") = splitDr("Guid")\par
    End If \par
    stockDr("IsWorking") = 0\par
    stockDr("LockedByMovement") = isBatchDiff\par
    stockDr("UpdateBy") = userName\par
    stockDr("UpdateDate") = Date.Now\par
    \par
    itemDt.Save()\par
    stockDt.Save()\par
    \par
    ' \'b2\'ee\'d2\'ec\'c5\'fa\'b4\'ce\'b5\'f7\'d5\'fb\par
    If isBatchDiff Then\par
        \par
        Dim movementID As String = Guid.NewGuid.ToString\par
        ' \'b4\'a6\'c0\'ed\'bf\'e2\'b4\'e6\'b5\'f7\'d5\'fb\'c0\'e0\'d0\'cd\'a3\'ac\'c8\'f4SAP\'c5\'fa\'b4\'ce\'ce\'de\'b2\'ee\'d2\'ec\'d6\'b4\'d0\'d0\'b1\'be\'b5\'d8\'c5\'fa\'b4\'ce\'b5\'f7\'d5\'fb\'a3\'bb\'b7\'f1\'d4\'f2\'d5\'fd\'b3\'a3\'c5\'fa\'b4\'ce\'b5\'f7\'d5\'fb\par
        Dim moveType As String = IIf(tallyMasterBatch = stockDr("SAPBatch"), "X01", "301")\par
        \par
        ' \'bb\'f1\'c8\'a1\'d0\'c2\'b5\'c4\'bf\'e2\'b4\'e6\'b5\'f7\'d5\'fb\'b5\'a5\'ba\'c5\par
        Dim prefixDr As DataRow = DataTables("InternalParameters").Find("ParameterType = 'DocPrefix' And ParameterName = 'StockMovement'")\par
        If prefixDr Is Nothing Then\par
            res("error") = "\'bf\'e2\'b4\'e6\'b5\'f7\'d5\'fb\'b5\'a5\'be\'dd\'c7\'b0\'d7\'ba\'ce\'b4\'c5\'e4\'d6\'c3\'a3\'ac\'c7\'eb\'c1\'aa\'cf\'b5\'b9\'dc\'c0\'ed\'d4\'b1\'a3\'a1"\par
            Functions.Execute("WebService_APIResult", e, res)\par
            Return ""\par
        End If\par
        \par
        Dim prefix As String = prefixDr("ParameterValue") & Format(Date.Today, "yyMMdd")\par
        Dim suffixLength As Integer = Val(prefixDr("ParameterValueExt"))\par
        If suffixLength = 0 Then\par
            suffixLength = 4\par
        End If\par
        cmd.CommandText = "Select Right(Max(DocNo)," & suffixLength & ") From WMSStockMovementHead Where Del = 0 And DocNo Like '" & prefix & "%'"\par
        Dim maxNo As String = cmd.ExecuteScalar\par
        Dim maxNum As Integer = 1\par
        If isnumeric(maxNo) Then\par
            maxNum = CInt(maxNo) + 1\par
        End If\par
        Dim docNo As String = prefix & maxNum.ToString.PadLeft(suffixLength, "0")\par
        \par
        ' \'b4\'b4\'bd\'a8\'bf\'e2\'b4\'e6\'b5\'f7\'d5\'fb\'b5\'a5\'b1\'ed\'cd\'b7\par
        cmd.CommandText = "Insert Into WMSStockMovementHead (CreateDate,CreateBy,Guid,DocNo,Remark,Del,MovementTypes,TotalMoveQty,IsClosed,APIExecuteStatus,IsLocked) Values (GETDATE(),'System',?,?,?,0,?,?,0,?,0)"\par
        cmd.Parameters.Add("@Guid", movementID)\par
        cmd.Parameters.Add("@DocNo", docNo)\par
        cmd.Parameters.Add("@Remark", "\'b3\'f6\'bf\'e2\'c0\'ed\'bb\'f5\'c5\'fa\'b4\'ce\'b2\'ee\'d2\'ec\'b5\'f7\'d5\'fb")\par
        cmd.Parameters.Add("@MovementTypes", moveType)\par
        cmd.Parameters.Add("@TotalMoveQty", tallyQty)\par
        cmd.Parameters.Add("@APIExecuteStatus", IIf(moveType = "301", 1, 0))\par
        cmd.ExecuteNonQuery\par
        cmd.Parameters.Clear\par
        \par
        ' \'b4\'b4\'bd\'a8\'bf\'e2\'b4\'e6\'b5\'f7\'d5\'fb\'b5\'a5\'b1\'ed\'cc\'e5\par
        cmd.CommandText = "Insert Into WMSStockMovementItem (Guid,HeadID,Plant,StorageLocation,MatCode,BatchNo,ToPlant,ToLocation,ToMaterial,ToBatch,StockType,ToStockType,GoodsMovementType,MoveQty,StockID," & _\par
        "Del,IsExecuted,CreateBy,CreateDate,FromUnrestrictedQty,FromBlockedQty,ToUnrestrictedQty,ToBlockedQty,PalletID,ActualExpiration,ToExpiration,StockStatus,PalletNo,isBatchManagementRequired,FromSAPBatch,ToSAPBatch) " & _\par
        "Values(NEWID(),?,?,?,?,?,?,?,?,?,'UNR','UNR',?,?,?,0,0,'System',GETDATE(),?,0,?,0,?,?,?,?,?,1,?,?)"\par
        cmd.Parameters.Add("@HeadID", movementID)\par
        cmd.Parameters.Add("@Plant", stockDr("Plant"))\par
        cmd.Parameters.Add("@StorageLocation", stockDr("StorageLocation"))\par
        cmd.Parameters.Add("@MatCode", stockDr("MatCode"))\par
        cmd.Parameters.Add("@BatchNo", stockDr("BatchNo"))\par
        cmd.Parameters.Add("@ToPlant", stockDr("Plant"))\par
        cmd.Parameters.Add("@ToLocation", stockDr("StorageLocation"))\par
        cmd.Parameters.Add("@ToMaterial", stockDr("MatCode"))\par
        cmd.Parameters.Add("@ToBatch", tallyBatch)\par
        cmd.Parameters.Add("@GoodsMovementType", moveType)\par
        cmd.Parameters.Add("@MoveQty", tallyQty)\par
        cmd.Parameters.Add("@StockID", stockDr("Guid"))\par
        cmd.Parameters.Add("@FromUnrestrictedQty", - tallyQty)\par
        cmd.Parameters.Add("@ToUnrestrictedQty", tallyQty)\par
        cmd.Parameters.Add("@PalletID", stockDr("PalletID"))\par
        cmd.Parameters.Add("@ActualExpiration", stockDr("ActualExpiration"))\par
        cmd.Parameters.Add("@ToExpiration", tallyExpiration)\par
        cmd.Parameters.Add("@StockStatus", stockDr("StockStatus"))\par
        cmd.Parameters.Add("@PalletNo", stockDr("PalletNo"))\par
        cmd.Parameters.Add("@FromSAPBatch", stockDr("SAPBatch"))\par
        cmd.Parameters.Add("@ToSAPBatch", tallyMasterBatch)\par
        cmd.ExecuteNonQuery\par
        cmd.Parameters.Clear\par
        \par
    End If\par
    \par
    cmd.Commit()\par
Catch ex As Exception\par
    output.show(ex.tostring)\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d4\'d9\'ca\'d4!"\par
    cmd.Rollback()\par
End Try\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutLabing\\BWOutOrderLabelingGetList\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "BWOutProcessing", "*", " And Del = 0 And ProcessingStatus = 0", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutTally\\BWOutOrderTallyGetData\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select t.Guid,t.MatCode,t.BatchNo,t.GoodsNum,t.RetentionQty,t.CIQQty,t.MissingQty,t.ShelveQty,t.IsBatchDiff,t.BatchNo As TallyBatch,o.BLNo,o.ActualExpiration As TallyExpiration,o.oPCB,o.ActualExpiration,o.PalletNo,o.SSCC,o.oBarCode,o.oMatType,o.oCName,(Case When (Select isBatchManagementRequired From NEOProductMaster Where Del = 0 And skuCode = t.MatCode) = 1 Then '\'ca\'c7' Else '\'b7\'f1' End) As isBatchManagementRequired From BWOutTally t Left Join View_BWOutOrderItemEx o On t.ItemID = o.Guid Where t.Del = 0 And t.Guid = ?"\par
cmd.Parameters.Add("@Guid", xData("id").ToString)\par
Dim dt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
If dt.DataRows.Count = 0 Then\par
    res("error") = "\'c0\'ed\'bb\'f5\'c3\'f7\'cf\'b8\'b2\'bb\'b4\'e6\'d4\'da\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
Else\par
    res("data") = Functions.Execute("Sys_DataRow2JObject", dt.DataRows(0), False)\par
End If\par
\par
\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutTally\\BWOutOrderTallyGetList\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "View_BWOutTallyItemEx ", "_Identify,Guid,BLNo,PalletNo,MatCode,BatchNo,GoodsNum", " And Del = 0 And TallyStatus = 0", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPackaging\\BWOutPackagingCancel\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim palletID As String = xData("palletId").ToString\par
\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
\par
Dim res As New JObject\par
Dim resJo As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
Try\par
    cmd.BeginTransaction()\par
    \par
    ' \'d0\'a3\'d1\'e9\'bf\'e2\'b4\'e6\'ca\'c7\'b7\'f1\'cb\'f8\'b6\'a8\par
    cmd.CommandText = "Select _Identify From StockDetail Where Del = 0 And (LockedByMovement = 1 Or LockedByTaking = 1 Or LockedByAPI = 1) And PalletID = ? "\par
    cmd.Parameters.Add("@PalletID", palletID)\par
    If cmd.ExecuteScalar > 0 Then\par
        res("error") = "\'cd\'d0\'c5\'cc\'d6\'d0\'b4\'e6\'d4\'da\'bf\'e2\'b4\'e6\'cb\'f8\'b6\'a8\'cf\'ee\'a3\'ac\'ce\'de\'b7\'a8\'b3\'b7\'cf\'fa!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    ' \'d0\'a3\'d1\'e9\'cc\'e1\'bd\'bb\'b5\'c4\'ca\'fd\'be\'dd\'ca\'c7\'b7\'f1\'d7\'b0\'b3\'b5\par
    cmd.CommandText = "Select _Identify From BWOutPickingItem Where Del = 0 And LoadingStatus = 1 And NewPalletID = ?"\par
    If cmd.ExecuteScalar > 0 Then\par
        res("error") = "\'cd\'d0\'c5\'cc\'d2\'d1\'d7\'b0\'b3\'b5\'a3\'ac\'ce\'de\'b7\'a8\'d6\'b1\'bd\'d3\'b3\'b7\'cf\'fa\'a3\'a1"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    cmd.Parameters.Clear\par
    Output.Show(111)\par
    \par
    ' \'b4\'a6\'c0\'ed\'bf\'e2\'b4\'e6\'c3\'f7\'cf\'b8\par
    cmd.CommandText = "Update StockDetail Set IsWorking = 1, UpdateDate = GETDATE(), UpdateBy = ? Where Del = 0 And IsWorking = 0 And StockStatus = 'OutPicking' And PalletID = ?"\par
    cmd.Parameters.Add("@UpdateBy", userName)\par
    cmd.Parameters.Add("@PalletID", palletID)\par
    cmd.ExecuteNonQuery\par
    \par
    ' \'b3\'b7\'cf\'fa\'b4\'f2\'b0\'fc\par
    cmd.CommandText = "Update BWOutPickingItem Set UpdateDate = GETDATE(), UpdateBy = ?, PackagingStatus = 0, PackagingBy = Null, PackagingDate = Null Where Del = 0 And PackagingStatus = 1 And NewPalletID = ?"\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear()\par
\par
    cmd.Commit()\par
Catch ex As Exception\par
    output.show(ex.tostring)\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d4\'d9\'ca\'d4!"\par
    cmd.Rollback()\par
End Try\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPackaging\\BWOutPackagingConfirm\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
\par
Dim res As New JObject\par
Dim resJo As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
Try\par
    cmd.BeginTransaction()\par
    \par
    ' \'d0\'a3\'d1\'e9\'bf\'e2\'b4\'e6\'ca\'c7\'b7\'f1\'cb\'f8\'b6\'a8\par
    cmd.CommandText = "Select _Identify From StockDetail Where Del = 0 And (LockedByMovement = 1 Or LockedByTaking = 1 Or LockedByAPI = 1) And PalletID = ? "\par
    cmd.Parameters.Add("PalletID", xData("id").ToString)\par
    If cmd.ExecuteScalar > 0 Then\par
        res("error") = "\'cd\'d0\'c5\'cc\'d6\'d0\'b4\'e6\'d4\'da\'bf\'e2\'b4\'e6\'cb\'f8\'b6\'a8\'cf\'ee\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    cmd.Parameters.Clear\par
    \par
    ' \'b8\'fc\'d0\'c2\'bf\'e2\'b4\'e6\'c3\'f7\'cf\'b8\'b9\'a4\'d7\'f7\'d7\'b4\'cc\'ac\par
    cmd.CommandText = "Update StockDetail Set IsWorking = 0, UpdateDate = GETDATE(), UpdateBy = ? Where Del = 0 And IsWorking = 1 And StockStatus = 'OutPicking' And PalletID = ?"\par
    cmd.Parameters.Add("UpdateBy", userName)\par
    cmd.Parameters.Add("PalletID", xData("id").ToString)\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear\par
    \par
    ' \'b8\'fc\'d0\'c2\'bc\'f0\'bb\'f5\'c3\'f7\'cf\'b8\'b4\'f2\'b0\'fc\'d7\'b4\'cc\'ac\par
    cmd.CommandText = "Update BWOutPickingItem Set UpdateDate = GETDATE(), UpdateBy = ?, PackagingStatus = 1, PackagingBy = ?, PackagingDate = GETDATE() Where Del = 0 And PackagingStatus = 0 And NewPalletID = ?"\par
    cmd.Parameters.Add("UpdateBy", userName)\par
    cmd.Parameters.Add("PackagingBy", userName)\par
    cmd.Parameters.Add("PalletID", xData("id").ToString)\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear\par
\par
    cmd.Commit()\par
Catch ex As Exception\par
    output.show(ex.tostring)\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d4\'d9\'ca\'d4!"\par
    cmd.Rollback()\par
End Try\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPackaging\\BWOutPackagingGetList\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
res("data") = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "(Select NewPalletID As _Identify, HeadID,NewPalletID,NewPalletNo,NewSSCC,Max(PickingDate) As PickingDate,Sum(PickingQty) As PickingQty From BWOutPickingItem Where Del = 0 And PackagingStatus = 0 Group By HeadID,NewPalletID,NewPalletNo,NewSSCC) tmp", "*", "", False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPackaging\\BWOutPackagingGetPallet\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
' \'bb\'f1\'c8\'a1\'cd\'d0\'c5\'cc\'d0\'c5\'cf\'a2\par
cmd.CommandText = "Select PalletNo, SSCC From Record4Pallet Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@NewPalletID", xData("id").ToString)\par
Dim Values = cmd.ExecuteValues\par
If Values.Count <> 2 Then\par
    res("error") = "\'cd\'d0\'c5\'cc\'d0\'c5\'cf\'a2\'bb\'f1\'c8\'a1\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'d6\'d8\'ca\'d4\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
resJo("palletNo") = Values("PalletNo").ToString\par
resJo("sscc") = Values("SSCC").ToString\par
\par
' \'bb\'f1\'c8\'a1\'cd\'d0\'c5\'cc\'bc\'f0\'bb\'f5\'d0\'c5\'cf\'a2\par
cmd.CommandText = "Select * FRom BWOutPickingItem Where Del = 0 And PackagingStatus = 0 And NewPalletID = ?"\par
Dim dt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
resJo("picking") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
\par
res("data") = resJo\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPackaging\\BWOutPackagingGetPalletIdBySSCC\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As New JObject\par
res("data") = resJo\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
' \'bb\'f1\'c8\'a1\'cd\'d0\'c5\'cc\'d0\'c5\'cf\'a2\par
cmd.CommandText = "Select Guid From Record4Pallet Where Del = 0 And PalletType = 'O' And SSCC = ?"\par
cmd.Parameters.Add("@SSCC", xData("sscc").ToString)\par
Dim palletID As String = cmd.ExecuteScalar\par
If palletID = "" Then\par
    res("error") = "SSCC\'b2\'bb\'b4\'e6\'d4\'da\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
resJo("palletId") = palletID\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPackaging\\BWOutPackagingGetRecord\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
' \'bb\'f1\'c8\'a1\'b4\'f2\'b0\'fc\'bc\'c7\'c2\'bc\par
cmd.CommandText = "Select NewPalletID,NewPalletNo,NewSSCC,PackagingDate From BWOutPickingItem Where Del = 0 And PackagingStatus = 1 And LoadingStatus = 0 Group By NewPalletID,NewPalletNo,NewSSCC,PackagingDate Order By PackagingDate Desc"\par
Dim dt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
\par
res("data") = Functions.Execute("Sys_DataTable2Json", dt, False)\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPackaging\\BWOutPackagingGetUnFinishDoc\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As JObject = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "View_BWOutPickingHeadEx", "*", "And Del = 0 And PackagingStatus = 0 And GoodsNum > 0", False)\par
'Functions.Execute("Sys_ReplaceWithInternalParameter", resJo("items"), "OutDeliveryType", "OutDeliveryType", "ParameterName")\par
\par
res("data") = resJo\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPicking\\BWOutPickingGetOPList\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As New JObject\par
res("data") = resJo\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
cmd.CommandText = "Select OutHeadID,BoundOutbound,IsClosed From BWOutPickingHead Where Del = 0 And Guid = ?"\par
cmd.Parameters.Add("@Guid", xData("id").ToString)\par
Dim Values = cmd.ExecuteValues\par
If Values.count = 0 Then\par
    res("error") = "\'bc\'f0\'bb\'f5\'b5\'a5\'d0\'c5\'cf\'a2\'bb\'f1\'c8\'a1\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
If CBool(Values("IsClosed")) Then\par
    res("error") = "\'bc\'f0\'bb\'f5\'b5\'a5\'d2\'d1\'b9\'d8\'b1\'d5\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If \par
cmd.Parameters.Clear\par
resJo("bound") = Values("BoundOutbound").ToString\par
\par
' \'bb\'f1\'c8\'a1\'b0\'f3\'b6\'a8\'b5\'c4Outbound\'d0\'c5\'cf\'a2\par
cmd.CommandText = "Select DeliveryDocumentItem,Material,DeliveryQuantity,(Select Sum(PickingQty) From BWOutPickingItem Where Del = 0 And Outbound = i.DeliveryDocument And MatCode = i.Material) As PickingQty From NEOOutboundDeliveryItem i Where i.Del = 0 And i.DeliveryQuantity > 0 And i.DeliveryDocument = ? Order By i.DeliveryDocumentItem"\par
cmd.Parameters.add("@DeliveryDocument", Values("BoundOutbound"))\par
Dim obdDt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
resJo("obd") = Functions.Execute("Sys_DataTable2Json", obdDt, False)\par
\par
' \'bb\'f1\'c8\'a1\'b6\'d4\'d3\'a6\'b3\'f6\'bf\'e2\'b6\'a9\'b5\'a5\'d2\'d1\'c0\'ed\'bb\'f5\'b5\'c4\'b2\'fa\'c6\'b7\par
'And LockedByMovement = 0 And LockedByTaking = 0 And LockedByAPI = 0 \par
cmd.CommandText = "Select * From View_StockDetailInBWOutTally Where Del = 0 And UnrestrictedQty > 0 And HeadID = ? Order By LEN(PalletNo),PalletNo,MatCode"\par
cmd.Parameters.add("@HeadID", Values("OutHeadID"))\par
Dim tallyDt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
resJo("tally") = Functions.Execute("Sys_DataTable2Json", tallyDt, False)\par
\par
' \'bb\'f1\'c8\'a1\'d2\'d1\'bc\'f0\'bb\'f5\'b5\'c4\'b2\'fa\'c6\'b7\par
cmd.CommandText = "Select HeadID,NewPalletID,NewPalletNo,NewSSCC,PackagingStatus,Max(PickingDate) As PickingDate,Sum(PickingQty) As PickingQty From BWOutPickingItem Where Del = 0 And HeadID = ? Group By HeadID,NewPalletID,NewPalletNo,NewSSCC,PalletType,PackagingMaterial,PackagingStatus,PickingBy"\par
cmd.Parameters.Add("@Guid", xData("id").ToString)\par
Dim pickingDt As DataTable = cmd.ExecuteReader\par
cmd.Parameters.Clear\par
resJo("picking") = Functions.Execute("Sys_DataTable2Json", pickingDt, False)\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPicking\\BWOutPickingGetUnFinishDoc\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As JObject = Functions.Execute("WebService_GetDataTableWithPagingLimits", e, "sys", "View_BWOutPickingHeadEx", "*", "And Del = 0 And PickingType = 'Normal' And IsClosed = 0", False)\par
Functions.Execute("Sys_ReplaceWithInternalParameter", resJo("items"), "OutDeliveryType", "OutDeliveryType", "ParameterName")\par
\par
res("data") = resJo\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPicking\\BWOutPickingItemAdd\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim res As New JObject\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim headID As String = xData("id").Tostring\par
Dim mutID As String = xData("ids").tostring.Replace("[", "(").Replace("]", ")").Replace("""", "'")\par
Dim valueJa As JArray = JArray.Parse(xData("values").Tostring)\par
'Dim huJo As JObject = JObject.Parse(xData("typ").Tostring)\par
\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
\par
' \'cc\'e1\'bd\'bb\'ca\'fd\'be\'dd\'d0\'a3\'d1\'e9\par
If valueJa.Count = 0 Then\par
    res("error") = "\'cc\'e1\'bd\'bb\'ca\'fd\'be\'dd\'d2\'ec\'b3\'a3!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If \par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
' \'d0\'a3\'d1\'e9\'bc\'f0\'bb\'f5\'b5\'a5\'ca\'c7\'b7\'f1\'b9\'d8\'b1\'d5\par
cmd.CommandText = "Select _Identify From BWOutPickingHead Where Del = 0 And IsClosed = 1 And Guid = ?"\par
cmd.Parameters.Add("@Guid", headID)\par
If cmd.ExecuteScalar > 0 Then\par
    res("error") = "\'bc\'f0\'bb\'f5\'b5\'a5\'d2\'d1\'b9\'d8\'b1\'d5\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
cmd.Parameters.Clear()\par
\par
' \'bb\'f1\'c8\'a1\'b0\'fc\'d7\'b0\'b2\'c4\'c1\'cf\'d1\'a1\'cf\'ee\par
Dim packId As String = DataTables("InternalParameters").GetComboListString("ParameterValue", "ParameterType = 'PackId' And IsActived = 1 And IsDefault = 1")\par
If packId = "" Then\par
    packId = "PAL_27"\par
End If \par
\par
' \'bb\'f1\'c8\'a1\'cd\'d0\'c5\'cc\'b9\'e6\'b8\'f1\'d1\'a1\'cf\'ee\par
cmd.CommandText = "Select DictionaryValue From BaseDictionary Where Del = 0 And IsActived = 1 And IsDefault = 1 And DictionaryType = 'PalletType'"\par
Dim palletType As String = cmd.ExecuteScalar\par
If palletType = "" Then\par
    palletType = "1.2*1*1.1"\par
End If \par
\par
Try\par
    cmd.BeginTransaction()\par
    ' \'d0\'a3\'d1\'e9\'bf\'e2\'b4\'e6\'ca\'c7\'b7\'f1\'cb\'f8\'b6\'a8\par
    cmd.CommandText = "Select _Identify From StockDetail Where Del = 0 And (LockedByMovement = 1 Or LockedByTaking = 1 Or LockedByAPI = 1) And Guid In " & mutID\par
    If cmd.ExecuteScalar > 0 Then\par
        res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'cc\'e1\'bd\'bb\'b5\'c4\'ca\'fd\'be\'dd\'d6\'d0\'b4\'e6\'d4\'da\'bf\'e2\'b4\'e6\'cb\'f8\'b6\'a8\'cf\'ee\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    ' \'bb\'f1\'c8\'a1\'b1\'ed\'cd\'b7\'ca\'fd\'be\'dd\par
    cmd.CommandText = "Select p.PickingDocNo,p.PickingType,p.BoundOutbound,h.YUB,h.HBL From BWOutPickingHead p Left Join BWOutOrderHead h On p.OutHeadID = h.Guid Where p.Del = 0 And p.Guid = '" & headID & "'"\par
    Dim Values = cmd.ExecuteValues\par
    Dim boundOBD As String = Values("BoundOutbound").Tostring\par
    Dim yub As String = Values("YUB").Tostring\par
    \par
    Dim opid As String = Guid.NewGuid.Tostring\par
    \par
    ' \'bb\'f1\'c8\'a1\'cc\'e1\'bd\'bb\'ca\'fd\'be\'dd\'b6\'d4\'d3\'a6\'b5\'c4\'b3\'f6\'bf\'e2\'c0\'ed\'bb\'f5\'c3\'f7\'cf\'b8\par
    cmd.CommandText = "Select * From View_StockDetailInBWOutTally Where Del = 0 And Guid In " & mutID\par
    Dim outTallyDt As DataTable = cmd.ExecuteReader\par
\par
    Dim palletIds As List(Of String)\par
    palletIds = outTallyDt.GetValues("PalletID")\par
    If palletIds.Count = 0 Then\par
         res("error") = "\'cc\'e1\'bd\'bb\'b5\'c4\'cd\'d0\'c5\'cc\'d2\'ec\'b3\'a3\'a3\'ac\'c7\'eb\'b7\'b5\'bb\'d8\'d6\'d8\'ca\'d4!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If \par
    \par
    ' \'b9\'b9\'d4\'ec\'bc\'f0\'bb\'f5Item\'b1\'ed\par
    cmd.CommandText = "Select * From BWOutPickingItem Where Del = 0 And HeadID = '" & xData("id").Tostring & "'"\par
    Dim pickItemDt As DataTable = cmd.ExecuteReader(True)\par
    \par
    ' \'b4\'b4\'bd\'a8\'b3\'f6\'bf\'e2\'bc\'f0\'bb\'f5\'cd\'d0\'c5\'cc\par
    Dim prefix As String = "9TC000" & Format(Date.Today, "yyyyMMdd")\par
    cmd.CommandText = "Select Right(Max(NewSSCC),4) From BWOutPickingItem Where Del = 0 And NewSSCC Like '" & prefix & "%'"\par
    Dim maxNo As String = cmd.ExecuteScalar\par
    Dim maxNum As Integer = 1\par
    If maxNo <> "" Then\par
        maxNum = CInt(maxNo) + 1\par
    End If\par
    Dim newSSCC As String = prefix & maxNum.ToString.PadLeft(4, "0")\par
    Dim TotalQty As Integer = 0\par
    For i As Integer = 0 To valueJa.count - 1\par
        TotalQty = TotalQty + Val(valueJa(i)("PickingQty"))\par
    Next\par
    Dim pickPalletID As String = Guid.NewGuid.ToString\par
    Dim pickPalletNo As String = Functions.Execute("Func_GetPickingPalletNo", cmd, palletIds, TotalQty)\par
    Output.Show("NEWPalletNo:" & pickPalletNo)\par
    \par
    '\'d0\'c2\'d4\'f6\'cd\'d0\'c5\'cc\'bc\'c7\'c2\'bc\par
    cmd.CommandText = "Insert Into Record4Pallet(Guid,HBL,SSCC,PalletType,PalletNo,CreateBy,CreateDate,Del,DocID,PackId) Values(?,?,?,'O',?,?,GETDATE(),0,?,?)"\par
    cmd.Parameters.Add("@Guid", pickPalletID)\par
    cmd.Parameters.Add("@HBL", Values("HBL"))\par
    cmd.Parameters.Add("@SSCC", newSSCC)\par
    cmd.Parameters.Add("@PalletNo", pickPalletNo)\par
    cmd.Parameters.Add("@CreateBy", userName)\par
    cmd.Parameters.Add("@DocID", headID)\par
    cmd.Parameters.Add("@PackId", packId)\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear()\par
    Output.Show("\'b2\'e5\'c8\'eb\'cd\'d0\'c5\'cc\'bc\'c7\'c2\'bc")\par
    \par
    ' \'bc\'f0\'bb\'f5\'b4\'a6\'c0\'ed\par
    Dim pickingDate As Date = now\par
    For i As Integer = 0 To valueJa.count - 1\par
        Dim valueJo As JObject = valueJa(i)\par
        Output.Show(valueJo.ToString)\par
        Dim outTallyDr As DataRow = outTallyDt.Find("Guid = '" & valueJo("Guid").Tostring & "'")\par
        If outTallyDr IsNot Nothing Then\par
            Output.Show(i)\par
            Dim pickItemDr As DataRow = pickItemDt.AddNew()\par
            pickItemDr("Guid") = Guid.NewGuid.Tostring\par
            pickItemDr("HeadID") = headID\par
            pickItemDr("PickingDocNo") = Values("PickingDocNo")\par
            pickItemDr("PickingType") = Values("PickingType")\par
            pickItemDr("OutHeadID") = outTallyDr("OutHeadID")\par
            pickItemDr("OutItemID") = outTallyDr("ItemID")\par
            pickItemDr("TallyDetailID") = outTallyDr("TallyDetailID")\par
            pickItemDr("BLNo") = outTallyDr("BLNo")\par
            pickItemDr("YUB") = yub\par
            pickItemDr("Outbound") = boundOBD\par
            pickItemDr("StockID") = Guid.NewGuid.Tostring\par
            pickItemDr("StockParentID") = outTallyDr("Guid")\par
            pickItemDr("MatCode") = outTallyDr("MatCode")\par
            pickItemDr("BatchNo") = outTallyDr("BatchNo")\par
            pickItemDr("PickingQty") = CInt(valueJo("PickingQty"))\par
            ' \'d3\'c5\'cf\'c8\'b4\'f8\'d7\'df\'cc\'f9\'b1\'ea\'c9\'d9\'ca\'fd\'c1\'bf\par
            pickItemDr("MissingQty") = outTallyDr("MissingRemaining")\par
            pickItemDr("GoodsNum") = pickItemDr("PickingQty") + pickItemDr("MissingQty")\par
            ' \'b8\'f9\'be\'dd\'bc\'f0\'bb\'f5\'ca\'fd\'c1\'bf\'a3\'ac\'d3\'c5\'cf\'c8\'b4\'f8\'d7\'dfCIQ\'ca\'fd\'c1\'bf\par
            If pickItemDr("PickingQty") >= outTallyDr("CIQRemaining") Then\par
                pickItemDr("CIQQty") = outTallyDr("CIQRemaining")\par
            Else\par
                pickItemDr("CIQQty") = pickItemDr("PickingQty")\par
            End If\par
            ' \'b8\'f9\'be\'dd\'bc\'f0\'bb\'f5\'ca\'fd\'c1\'bf - CIQ\'ba\'f3\'b5\'c4\'ca\'a3\'d3\'e0\'ca\'fd\'c1\'bf\'a3\'ac\'b4\'f8\'d7\'df\'b8\'e9\'d6\'c3\'ca\'fd\'c1\'bf\par
            Dim nociqPickingQty = pickItemDr("PickingQty") - pickItemDr("CIQQty")\par
            If nociqPickingQty > outTallyDr("ShelveRemaining") Then\par
                pickItemDr("ShelveQty") = outTallyDr("ShelveRemaining")\par
            Else \par
                pickItemDr("ShelveQty") = nociqPickingQty\par
            End If\par
            ' \'c8\'e7\'d3\'d0\'b8\'e9\'d6\'c3\'a3\'ac\'b4\'f8\'c8\'eb\'b8\'e9\'d6\'c3\'d4\'ad\'d2\'f2\par
            If pickItemDr("ShelveQty") > 0 Then\par
                pickItemDr("ShelveReason") = outTallyDr("ShelveReason")\par
            End If\par
            pickItemDr("CreateBy") = userName\par
            pickItemDr("CreateDate") = pickingDate\par
            pickItemDr("PickingBy") = userName\par
            pickItemDr("PickingDate") = pickingDate\par
            pickItemDr("OriginalPalletID") = outTallyDr("PalletID")\par
            pickItemDr("OriginalPalletNo") = outTallyDr("PalletNo")\par
            pickItemDr("OriginalSSCC") = outTallyDr("SSCC")\par
            pickItemDr("NewPalletID") = pickPalletID\par
            pickItemDr("NewPalletNo") = pickPalletNo \par
            pickItemDr("NewSSCC") = newSSCC \par
            pickItemDr("PalletType") = palletType\par
            pickItemDr("PackagingMaterial") = packId \par
            pickItemDr("OPID") = opid\par
            \par
            ' \'b5\'b1\'cc\'f9\'b1\'ea\'c9\'d9 + CIQ + \'b8\'e9\'d6\'c3 > 0, \'b8\'fc\'d0\'c2\'c0\'ed\'bb\'f5\'ca\'fd\'be\'dd\'d6\'d0\'b6\'d4\'d3\'a6\'b5\'c4\'ca\'a3\'d3\'e0\'ca\'fd\'c1\'bf\par
            If pickItemDr("MissingQty") + pickItemDr("CIQQty") + pickItemDr("ShelveQty") > 0 Then\par
                cmd.CommandText = "Update BWOutTally Set CIQRemaining = CIQRemaining - ?, MissingRemaining = MissingRemaining - ?,ShelveRemaining = ShelveRemaining - ? Where Del = 0 And Guid = ?"\par
                cmd.Parameters.Add("@CIQRemaining", pickItemDr("CIQQty"))\par
                cmd.Parameters.Add("@MissingRemaining", pickItemDr("MissingQty"))\par
                cmd.Parameters.Add("@ShelveRemaining", pickItemDr("ShelveQty"))\par
                cmd.Parameters.Add("@Guid", outTallyDr("TallyDetailID"))\par
                cmd.ExecuteNonQuery\par
                cmd.Parameters.Clear()\par
            End If \par
            \par
        End If \par
    Next\par
    Output.Show("\'bc\'f0\'bb\'f5\'b4\'a6\'c0\'ed")\par
    pickItemDt.save\par
    \par
    ' \'b4\'a6\'c0\'ed\'b3\'f6\'bf\'e2\'c0\'ed\'bb\'f5\'bf\'e2\'b4\'e6\par
    cmd.CommandText = "Update StockDetail Set UpdateDate = GETDATE(), UpdateBy = ?, StockQty = StockQty - p.GoodsNum, UnrestrictedQty = UnrestrictedQty - p.GoodsNum From StockDetail s Inner Join BWOutPickingItem p On s.Guid = p.StockParentID Where s.Del = 0 And p.OPID = ?"\par
    cmd.Parameters.Add("@UpdateBy", userName)\par
    cmd.Parameters.Add("@OPID", opid)\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear()\par
    \par
    ' \'d0\'c2\'d4\'f6\'bc\'f0\'bb\'f5\'b4\'f2\'b0\'fc\'bf\'e2\'b4\'e6\par
    cmd.CommandText = "Insert Into StockDetail (Guid,ParentID,StockStatus,WarehouseCode,OwnerCode,Plant,StorageLocation,MatCode,BatchNo,SAPBatch,ActualExpiration,AreaID,AreaName,PalletID,PalletNo,SSCC,StockQty,BlockedQty,UnrestrictedQty,InDetailID,InHeadID,HBL,Invoice,OutHeadID,BLNo,TallyDetailID,DocNo,CreateBy,CreateDate,Del,LockedByMovement,LockedByTaking,LockedByAPI,IsWorking) " & _\par
    "Select p.StockID,p.StockParentID,'OutPicking',s.WarehouseCode,s.OwnerCode,s.Plant,s.StorageLocation,s.MatCode,s.BatchNo,s.SAPBatch,s.ActualExpiration,s.AreaID,s.AreaName,p.NewPalletID,p.NewPalletNo,p.NewSSCC,p.GoodsNum,0,p.GoodsNum,s.InDetailID,s.InHeadID,s.HBL,s.Invoice,s.OutHeadID,s.BLNo,s.TallyDetailID,p.PickingDocNo,?,GETDATE(),0,0,0,0,1 From BWOutPickingItem p " & _\par
    "Left Join StockDetail s On p.StockParentID = s.Guid Where p.OPID = ?"\par
    cmd.Parameters.Add("@CreateBy", userName)\par
    cmd.Parameters.Add("@OPID", opid)\par
    cmd.ExecuteNonQuery\par
    cmd.Parameters.Clear()\par
    \par
    cmd.Commit()\par
Catch ex As Exception\par
    output.show(ex.tostring)\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d4\'d9\'ca\'d4!"\par
    cmd.Rollback()\par
End Try\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 BWOutPicking\\BWOutPickingItemRemove\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim res As New JObject\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim headID As String = xData("id").Tostring\par
Dim mutID As String = xData("ids").tostring.Replace("[", "(").Replace("]", ")").Replace("""", "'")\par
\par
Dim userName As String = Functions.Execute("WebService_GetJWTUser", e, "name")\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
cmd.CommandText = "Select _Identify From BWOutPickingHead Where Del = 0 And IsClosed = 1 And Guid = ?"\par
cmd.Parameters.Add("@Guid", headID)\par
If cmd.ExecuteScalar > 0 Then\par
    res("error") = "\'bc\'f0\'bb\'f5\'b5\'a5\'d2\'d1\'b9\'d8\'b1\'d5\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
cmd.Parameters.Clear()\par
\par
Try\par
    cmd.BeginTransaction()\par
    \par
    ' \'d0\'a3\'d1\'e9\'cd\'d0\'c5\'cc\'ca\'c7\'b7\'f1\'d2\'d1\'b4\'f2\'b0\'fc\par
    cmd.CommandText = "Select _Identify From BWOutPickingItem Where Del = 0 And PackagingStatus = 1 And NewPalletID In " & mutID\par
    If cmd.ExecuteScalar > 0 Then\par
        res("error") = "\'cc\'e1\'bd\'bb\'ca\'fd\'be\'dd\'d6\'d0\'b4\'e6\'d4\'da\'d2\'d1\'b4\'f2\'b0\'fc\'b5\'c4\'cd\'d0\'c5\'cc\'a3\'ac\'ce\'de\'b7\'a8\'d6\'b1\'bd\'d3\'b3\'b7\'cf\'fa\'a3\'a1"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    ' \'d0\'a3\'d1\'e9\'bf\'e2\'b4\'e6\'ca\'c7\'b7\'f1\'cb\'f8\'b6\'a8\par
    cmd.CommandText = "Select _Identify From StockDetail Where Del = 0 And (LockedByMovement = 1 Or LockedByTaking = 1 Or LockedByAPI = 1) And PalletID In " & mutID\par
    If cmd.ExecuteScalar > 0 Then\par
        res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'cc\'e1\'bd\'bb\'b5\'c4\'cd\'d0\'c5\'cc\'d6\'d0\'b4\'e6\'d4\'da\'bf\'e2\'b4\'e6\'cb\'f8\'b6\'a8\'cf\'ee\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'b2\'e9\'bf\'b4!"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        Return ""\par
    End If\par
    \par
    ' \'b8\'fc\'d0\'c2\'c0\'ed\'bb\'f5\'ca\'fd\'be\'dd\'a3\'ac\'bd\'ab\'cc\'f9\'b1\'ea\'c9\'d9/CIQ/\'b8\'e9\'d6\'c3\'ca\'fd\'c1\'bf\'b7\'c5\'bb\'d8\par
    cmd.CommandText = "Update BWOutTally Set UpdateDate = GETDATE(), UpdateBy = ?, MissingRemaining = MissingRemaining + p.MissingQty, CIQRemaining = CIQRemaining + p.CIQQty, ShelveRemaining = ShelveRemaining + p.ShelveQty From BWOutTally t Inner Join " & _\par
    "(Select Sum(MissingQty) As MissingQty,Sum(CIQQty) As CIQQty,Sum(ShelveQty) As ShelveQty,TallyDetailID From BWOutPickingItem Where Del = 0 And NewPalletID In " & mutID & " Group By TallyDetailID) p On t.Guid = p.TallyDetailID Where t.Del = 0"\par
    cmd.Parameters.Add("@UpdateBy", userName)\par
    cmd.ExecuteNonQuery\par
    \par
    ' \'b8\'fc\'d0\'c2\'c0\'ed\'bb\'f5\'bf\'e2\'b4\'e6\par
    cmd.CommandText = "Update StockDetail Set StockQty = StockQty + p.GoodsNum, UnrestrictedQty = UnrestrictedQty + p.GoodsNum, UpdateDate = GETDATE(),UpdateBy = ? From StockDetail s Inner Join " & _\par
    "(Select Sum(GoodsNum) As GoodsNum,StockParentID From BWOutPickingItem Where Del = 0 And NewPalletID In " & mutID & " Group By StockParentID) p On s.Guid = p.StockParentID Where s.Del = 0 And s.StockStatus = 'OutTally'"\par
    cmd.ExecuteNonQuery\par
    \par
    ' \'c9\'be\'b3\'fd\'bc\'f0\'bb\'f5\'bf\'e2\'b4\'e6\par
    cmd.CommandText = "Update StockDetail Set Del = 1, UpdateDate = GETDATE(), UpdateBy = ? Where Del = 0 And StockStatus = 'OutPicking' And PalletID In " & mutID\par
    cmd.ExecuteNonQuery\par
    \par
    ' \'c9\'be\'b3\'fd\'bf\'e2\'b4\'e6\'bc\'c7\'c2\'bc\par
    cmd.CommandText = "Update Record4Pallet Set Del = 1, UpdateDate = GETDATE(), UpdateBy = ? Where Del = 0 And PalletType = 'O' And Guid In " & mutID\par
    cmd.ExecuteNonQuery\par
    \par
    '\'c9\'be\'b3\'fd\'bc\'f0\'bb\'f5\'c3\'f7\'cf\'b8\par
    cmd.CommandText = "Update BWOutPickingItem Set Del = 1, UpdateDate = GETDATE(), UpdateBy = ? Where Del = 0 And NewPalletID In " & mutID\par
    cmd.ExecuteNonQuery\par
    \par
    cmd.Parameters.Clear\par
    \par
    cmd.Commit()\par
Catch ex As Exception\par
    output.show(ex.tostring)\par
    res("error") = "\'b2\'d9\'d7\'f7\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d4\'d9\'ca\'d4!"\par
    cmd.Rollback()\par
End Try\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 Auth\\Func_AddParentRoute\par
\par
\cf3 Dim routeNameJa As Jarray = Args(0)\par
Dim routeName As String = Args(1)\par
\par
Dim routeDr As DataRow = DataTables("InternalRouter").Find("RouteName = '" & routeName & "'")\par
If routeDr IsNot Nothing AndAlso routeDr("ParentName") <> "" AndAlso routeNameJa.ToObject(Of String()).Contains(routeDr("ParentName")) = False Then\par
    routeNameJa.Add(routeDr("ParentName"))\par
    Functions.Execute("Func_AddParentRoute", routeNameJa, routeDr("ParentName"))\par
End If\par
\par
\cf2 BWOutPicking\\Func_GetPickingPalletNo\par
\par
\cf3 Dim cmd As SQLCommand = Args(0)\par
Dim palletIds As List(Of String) = Args(1)\par
Dim totalQty As String = Args(2)\par
\par
Dim newPalletNo As String = ""\par
Dim baseID As String = palletIds(0)\par
\par
If palletIds.count = 1 Then\par
    ' \'b5\'a5\'b8\'f6\'cd\'d0\'c5\'cc\'a3\'ac\'bb\'f1\'c8\'a1\'c8\'eb\'bf\'e2\'cd\'d0\'c5\'cc\'ba\'c5\par
    cmd.CommandText = "Select PalletNo From Record4Pallet Where Del = 0 And Guid = '" & baseID & "'"\par
    Dim palletNo As String = cmd.ExecuteScalar\par
    newPalletNo = palletNo\par
    \par
    ' \'c5\'d0\'b6\'cf\'cd\'d0\'c5\'cc\'ca\'c7\'b7\'f1\'b2\'f0\'b7\'d6\par
    Dim splitMark = True\par
    \par
    ' \'bb\'f1\'c8\'a1\'c8\'eb\'bf\'e2\'cd\'d0\'c5\'cc\'ca\'b5\'ca\'d5\'ca\'fd\'c1\'bf\par
    cmd.CommandText = "Select ISNULL(Sum(ReceivedQty),0) From BWInOrderItem Where Del = 0 And PalletID = '" & baseID & "'"\par
    Dim receivedQty As Integer = cmd.ExecuteScalar\par
    \par
    ' \'bb\'f1\'c8\'a1\'c8\'eb\'bf\'e2\'cd\'d0\'c5\'cc\'c1\'f4\'d1\'f9\'ca\'fd\'c1\'bf\par
    cmd.CommandText = "Select ISNULL(Sum(StockQty),0) From StockDetail Where Del = 0 And StockStatus = 'Sample' And PalletID = '" & baseID & "'"\par
    Dim sampleQty As Integer = cmd.ExecuteScalar\par
    \par
    ' \'c5\'d0\'b6\'cf\'bc\'f0\'bb\'f5\'cd\'d0\'c5\'cc\'b5\'c4\'d7\'dc\'ca\'fd = \'ca\'b5\'ca\'d5\'ca\'fd\'c1\'bf-\'c1\'f4\'d1\'f9\'a3\'ac\'d4\'f2\'d1\'d8\'d3\'c3\'c8\'eb\'bf\'e2\'cd\'d0\'c5\'cc\'a3\'ac\'b7\'f1\'d4\'f2\'b2\'f0\'b7\'d6\'cd\'d0\'c5\'cc\'b1\'e0\'ba\'c5\par
    If totalQty = receivedQty - sampleQty Then \par
        splitMark = False\par
    End If\par
    \par
    ' \'c5\'d0\'b6\'cf\'c8\'eb\'bf\'e2\'cd\'d0\'c5\'cc\'b3\'fd\'c8\'a5\'c1\'f4\'d1\'f9\'b5\'c4\'d7\'dc\'bf\'e2\'b4\'e6\'ca\'fd\'d3\'eb\'cc\'e1\'bd\'bb\'bc\'f0\'bb\'f5\'ca\'fd\'ca\'c7\'b7\'f1\'d2\'bb\'d6\'c2\par
    'cmd.CommandText = "Select Count(_Identify) From StockDetail Where Del = 0 And StockQty > 0 And StockStatus Not In ('Inbound', 'Sample') And PalletID = '" & baseID & "'"\par
    'If cmd.ExecuteScalar = 1 Then\par
    'cmd.CommandText = "Select ISNULL(Sum(Case When StockStatus = 'Inbound' Then StockQty Else -StockQty End),0) From StockDetail Where Del = 0 And StockStatus In ('Inbound', 'Sample')  And PalletID = '" & baseID & "'"\par
    'If totalQty = cmd.ExecuteScalar Then \par
    'splitMark = False\par
    'End If\par
    'End If\par
    \par
    ' \'b4\'a6\'c0\'ed\'b2\'f0\'b7\'d6\'cd\'d0\'c5\'cc\'b1\'e0\'ba\'c5\par
    If splitMark Then\par
        cmd.CommandText = "Select NewPalletNo From BWOutPickingItem Where Del = 0 And NewPalletNo Like '" & palletNo & "%'"\par
        Dim palletDt As DataTable = cmd.ExecuteReader\par
        For i As Integer = 1 To 999\par
            newPalletNo = palletNo & "-" & + i\par
            If palletDt.Find("NewPalletNo = '" & newPalletNo & "'") Is Nothing Then\par
                Exit For\par
            End If\par
        Next\par
    End If \par
    \par
ElseIf palletIds.count > 1 Then\par
    ' \'b6\'e0\'b8\'f6\'cd\'d0\'c5\'cc,\'b2\'e9\'d5\'d2\'b3\'f6\'bf\'e2\'cd\'d0\'c5\'cc\'bc\'c7\'c2\'bc\par
    cmd.CommandText = "Select HBL From Record4Pallet Where Del = 0 And Guid = '" & baseID & "'"\par
    Dim hbl As String = cmd.ExecuteScalar\par
    Dim basePallet As String = Right(hbl, 6)\par
    cmd.CommandText = "Select NewPalletNo From BWOutPickingItem Where Del = 0 And NewPalletNo Like '" & basePallet & "%'"\par
    Dim palletDt As DataTable = cmd.ExecuteReader\par
    For i As Integer = 1 To 999\par
        newPalletNo = basePallet & "-G" & + i\par
        If palletDt.Find("NewPalletNo = '" & newPalletNo & "'") Is Nothing Then\par
            Exit For\par
        End If\par
    Next\par
End If\par
\par
Return newPalletNo\par
\par
\cf2 Auth\\Func_HandleAllRoute\par
\par
\cf3 Dim routerDt As DataTable = Args(0)\par
Dim routeDr As DataRow = Args(1)\par
Dim routerJa As JArray = Args(2)\par
\par
Dim routeJo As New JObject\par
routerJa.Add(routeJo)\par
\par
Dim items1() As String = \{"RouteName", "Path", "Redirect", "Component"\}\par
Dim items2() As String = \{"name", "path", "redirect", "component"\}\par
\par
For i As Integer = 0 To items1.Count - 1\par
    'If routeDr.IsNull(items1(i)) = False Then\par
        If routerDt.DataCols(items1(i)).IsBoolean Then\par
            routeJo(items2(i)) = CBool(routeDr(Items1(i)))\par
        Else\par
            routeJo(items2(i)) = routeDr(Items1(i)).tostring\par
        End If\par
    'End If\par
Next\par
\par
Dim metaJo As New JObject\par
routeJo("meta") = metaJo\par
\par
Dim metaItems1() As String = \{"MetaActiveIcon", "MetaActivePath", "MetaAffixTab", "MetaHideChildrenInMenu", "MetaHideInBreadcrumb", "MetaHideInMenu", "MetaHideInTab", "MetaIcon", "MetaKeepAlive", "MetaLink", "MetaOrder", "MetaTitle"\}\par
Dim metaItems2() As String = \{"activeIcon", "activePath", "affixTab", "hideChildrenInMenu", "hideInBreadcrumb", "hideInMenu", "hideInTab", "icon", "keepAlive", "link", "order", "title"\}\par
For i As Integer = 0 To metaItems1.Count - 1\par
    If routeDr.IsNull(metaItems1(i)) = False Then\par
        If routerDt.DataCols(metaItems1(i)).IsBoolean Then\par
            'If CBool(routeDr(metaItems1(i))) Then\par
                routeJo("meta")(metaItems2(i)) = CBool(routeDr(metaItems1(i)))\par
            'end if\par
        Else\par
            routeJo("meta")(metaItems2(i)) = routeDr(metaItems1(i)).tostring\par
        End If\par
    End If\par
Next\par
\par
Dim childDrs As List(Of DataRow)\par
childDrs = routerDt.Select("ParentName = '" & routeDr("RouteName") & "' And Del = 0","MetaOrder")\par
If childDrs.Count > 0 Then\par
    Dim childJa As New JArray\par
    routeJo("children") = childJa\par
    For Each childDr As DataRow In childDrs\par
        Functions.Execute("Func_HandleAllRoute", routerDt, childDr, childJa)\par
    Next\par
End If\par
\par
\cf2 Auth\\Func_HandleRoute\par
\par
\cf3 Dim routerDt As DataTable = Args(0)\par
Dim routeNameJa As JArray = Args(1)\par
Dim routeDr As DataRow = Args(2)\par
Dim routerJa As JArray = Args(3)\par
\par
\par
If routeNameJa.ToObject(Of String()).Contains(routeDr("RouteName")) OrElse routeDr("MetaHideInMenu") Then\par
\par
    Dim routeJo As New JObject\par
    routerJa.Add(routeJo)\par
    \par
    Dim items1() As String = \{"RouteName", "Path", "Redirect", "Component"\}\par
    Dim items2() As String = \{"name", "path", "redirect", "component"\}\par
    \par
    For i As Integer = 0 To items1.Count - 1\par
        If routerDt.DataCols(items1(i)).IsBoolean Then\par
            routeJo(items2(i)) = CBool(routeDr(Items1(i)))\par
        Else\par
            routeJo(items2(i)) = routeDr(Items1(i)).tostring\par
        End If\par
    Next\par
    \par
    Dim metaJo As New JObject\par
    routeJo("meta") = metaJo\par
    \par
    Dim metaItems1() As String = \{"MetaActiveIcon", "MetaActivePath", "MetaAffixTab", "MetaHideChildrenInMenu", "MetaHideInBreadcrumb", "MetaHideInMenu", "MetaHideInTab", "MetaIcon", "MetaKeepAlive", "MetaLink", "MetaOrder", "MetaTitle"\}\par
    Dim metaItems2() As String = \{"activeIcon", "activePath", "affixTab", "hideChildrenInMenu", "hideInBreadcrumb", "hideInMenu", "hideInTab", "icon", "keepAlive", "link", "order", "title"\}\par
    For i As Integer = 0 To metaItems1.Count - 1\par
        If routeDr.IsNull(metaItems1(i)) = False Then\par
            If routerDt.DataCols(metaItems1(i)).IsBoolean Then\par
                routeJo("meta")(metaItems2(i)) = CBool(routeDr(metaItems1(i)))\par
            Else\par
                routeJo("meta")(metaItems2(i)) = routeDr(metaItems1(i)).tostring\par
            End If\par
        End If\par
    Next\par
    \par
    Dim childDrs As List(Of DataRow)\par
    childDrs = routerDt.Select("ParentName = '" & routeDr("RouteName") & "' And Del = 0", "MetaOrder")\par
    If childDrs.Count > 0 Then\par
        Dim childJa As New JArray\par
        routeJo("children") = childJa\par
        For Each childDr As DataRow In childDrs\par
            Functions.Execute("Func_HandleRoute", routerDt, routeNameJa, childDr, childJa)\par
        Next\par
    End If\par
End If\par
\par
\cf2 QYWeixin\\QYWeixinAPIGetTicket\par
\par
\cf3 Dim accessToken As String = Args(0)\par
Dim sdk_noncestr As String = Args(1)\par
Dim sdk_timestamp As String = Args(2)\par
Dim sdk_url As String = Args(3)\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "log"\par
Output.Show("x01")\par
' \'b2\'e9\'d5\'d2\'be\'e0\'c0\'eb\'ca\'a7\'d0\'a75\'b7\'d6\'d6\'d3\'d2\'d4\'c9\'cf\'b5\'c4ticket\par
cmd.CommandText = "Select Top 1 Ticket From Log4QYWeixinJSAPITicket Where AccessToken = ? And DATEDIFF(ss,getDate(), ExpiresDate) > 300"\par
cmd.Parameters.Add("@AccessToken", accessToken)\par
Dim ticket As String = cmd.ExecuteScalar()\par
cmd.Parameters.Clear()\par
Output.Show("x02")\par
' \'ce\'b4\'d5\'d2\'b5\'bd\'bf\'c9\'d3\'c3ticket\'a3\'ac\'bb\'f1\'c8\'a1\'bb\'f1\'c8\'a1\'c6\'f3\'d2\'b5 jsapi_ticket\'b2\'a2\'bc\'c7\'c2\'bc\par
If ticket = "" Then\par
    System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12\par
    Dim url As String = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=\{0\}"\par
    Dim hc As New HttpClient(CExp(url, accessToken))\par
    Dim st As Date = now\par
    Try\par
        Dim ret As String = hc.GetData()\par
        ' \'c8\'e7\'b9\'fb\'ca\'a7\'b0\'dc,\'d4\'d9\'b3\'a2\'ca\'d4\'d2\'bb\'b4\'ce\par
        If ret = "" Then \par
            hc.GetData()\par
        End If\par
        \par
        Dim jo As JObject = JObject.Parse(ret)\par
        If jo("ticket") IsNot Nothing Then\par
            ticket = jo("ticket").ToString\par
            Dim expiresIn As Integer = CInt(jo("expires_in").tostring)\par
            \par
            ' \'cc\'ed\'bc\'d3\'bc\'c7\'c2\'bc\par
            cmd.CommandText = "Insert Into Log4QYWeixinJSAPITicket (Guid,AccessToken,Ticket,ExpiresIn,CreateDate,ExpiresDate) Values(NEWID(),?,?,?,?,?)"\par
            cmd.Parameters.Add("@AccessToken", accessToken)\par
            cmd.Parameters.Add("@Ticket", ticket)\par
            cmd.Parameters.Add("@ExpiresIn", expiresIn)\par
            cmd.Parameters.Add("@CreateDate", st)\par
            cmd.Parameters.Add("@ExpiresDate", st.AddSeconds(expiresIn))\par
            cmd.ExecuteNonQuery\par
            cmd.Parameters.Clear\par
        End If \par
    Catch ex As Exception\par
    End Try \par
End If\par
\par
' ticket\'b4\'a6\'c0\'ed\'d2\'ec\'b3\'a3\'a3\'ac\'b7\'b5\'bb\'d8\'bf\'d5\'d6\'b5\par
If ticket = "" Then\par
    Return ""\par
End If\par
\par
' JS-SDK \'c7\'a9\'c3\'fb\'cb\'e3\'b7\'a8\par
Dim str As String\par
Try\par
    Dim signature As String = CExp("jsapi_ticket=\{0\}&noncestr=\{1\}&timestamp=\{2\}&url=\{3\}", Ticket, sdk_noncestr, sdk_timestamp, sdk_url)\par
    str = Web.Security.FormsAuthentication.HashPasswordForStoringInConfigFile(signature, "SHA1").ToLower()\par
Catch ex As Exception\par
End Try\par
\par
Return str\par
\par
\cf2 QYWeixin\\QYWeixinApiGetUserByCode\par
\par
\cf3 Dim accessToken As String = Args(0)\par
Dim code As String = Args(1)\par
\par
Dim url As String = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=\{0\}&code=\{1\}"\par
\par
url = CExp(url, accessToken, code)\par
\par
Dim hc As New HttpClient(url)\par
Dim jo As JObject = JObject.Parse(hc.GetData)\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "log"\par
cmd.CommandText = "Insert Into Log4QYWeixinUserInfo (Guid,AccessToken,Code,CreateDate,ResponseBody) Values(NEWID(),?,?,GETDATE(),?)"\par
cmd.Parameters.Add("@AccessToken", accessToken)\par
cmd.Parameters.Add("@Code", code)\par
cmd.Parameters.Add("@ResponseBody", compressJson(jo))\par
cmd.ExecuteNonQuery\par
cmd.Parameters.Clear\par
\par
Return jo\par
\par
\cf2 QYWeixin\\QYWeixinApiGetUserInfoByUserID\par
\par
\cf3 Dim accessToken As String = Args(0)\par
Dim userid As String = Args(1)\par
\par
Dim url As String = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=\{0\}&userid=\{1\}"\par
\par
url = CExp(url, accessToken, userid)\par
\par
Dim hc As New HttpClient(url)\par
Dim jo As JObject = JObject.Parse(hc.GetData)\par
Return jo\par
\par
\cf2 QYWeixin\\QYWeixinGetSignature4JSSDK\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Dim url As String = xData("url").Tostring\par
\par
Output.Show("URL:" & url)\par
\par
Dim res As New JObject\par
Dim resJo As New JObject\par
res("data") = resJo\par
\par
Dim appid As String = "wwfe9b2e18dd30144f"\par
Dim timestamp As Integer = DateTimeOffset.UtcNow.ToUnixTimeSeconds()\par
Dim noncestr As String = Rand.NextString(16)\par
\par
Dim accessToken As String = Functions.Execute("Sys_GetAccessToken", "wwfe9b2e18dd30144f", "T1xNLyW378yzMt6GU6kAQoSI8z8NDy-URBBaQUZ8b6s")\par
Output.Show(111)\par
Dim signature As String = Functions.Execute("QYWeixinAPIGetTicket", accessToken , noncestr, timestamp, url) ' \'c9\'fa\'b3\'c9\'c8\'a8\'cf\'de\'d1\'e9\'d6\'a4\'c7\'a9\'c3\'fb\par
Output.Show(222)\par
resJo("signature") = signature\par
resJo("nonceStr") = noncestr\par
resJo("timestamp") = timestamp\par
'jo("appId") = appid\par
\par
Output.Show(resJo.ToString)\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 SysFunction\\Sys_DataRow2JObject\par
\par
\cf3 Dim dr As DataRow = args(0)\par
Dim typ As Boolean = Args(1)\par
\par
'typ = true \'b7\'b5\'bb\'d8\'bf\'d5\'d6\'b5\'ba\'cdjson\'b6\'d4\'cf\'f3\par
\par
Dim dataRowJo As New JObject\par
For Each dc As DataCol In dr.DataTable.DataCols\par
    If typ Then\par
        If dr.IsNull(dc.Name) Then\par
            dataRowJo(dc.Name) = Nothing\par
            Continue For\par
        End If \par
    End If \par
    \par
    If dc.IsString Then\par
        '\'ce\'c4\'b1\'be\'c1\'d0\'b4\'a6\'c0\'ed\par
        dataRowJo(dc.Name) = CStr(dr(dc.Name))\par
        If typ Then\par
            If dr(dc.Name).StartsWith("[") AndAlso dr(dc.Name).EndsWith("]")Then\par
                Dim itemJa As New JArray\par
                itemJa = JArray.Parse(CStr(dr(dc.Name)))\par
                dataRowJo(dc.Name) = itemJa\par
            ElseIf dr(dc.Name).StartsWith("\{") AndAlso dr(dc.Name).EndsWith("\}")Then\par
                Dim itemJo As New JObject\par
                itemJo = JObject.Parse(CStr(dr(dc.Name)))\par
                dataRowJo(dc.Name) = itemJo\par
            End If\par
        End If \par
    Else If dc.IsBoolean Then\par
    '\'c2\'df\'bc\'ad\'c1\'d0\'b4\'a6\'c0\'ed\par
    Dim bo As Boolean = dr(dc.Name)\par
    dataRowJo(dc.Name) = bo\par
    Else If dc.IsNumeric Then\par
    '\'ca\'fd\'d6\'b5\'c1\'d0\'b4\'a6\'c0\'ed,VARLET\'b0\'f3\'b6\'a8\'ce\'c4\'b1\'be\par
    dataRowJo(dc.Name) = Val(dr(dc.Name)).ToString\par
    Else If dc.IsDate Then\par
        '\'c8\'d5\'c6\'da\'c1\'d0\'b4\'a6\'c0\'ed\par
        If dr.Isnull(dc.Name) = False Then\par
            dataRowJo(dc.Name) = CStr(dr(dc.Name))\par
        Else\par
            dataRowJo(dc.Name) = ""\par
        End If\par
    End If\par
Next\par
If dr.DataTable.DataCols.Contains("_Identify") Then\par
    dataRowJo("_Identify") = CInt(dr("_Identify"))\par
End If\par
If dr.DataTable.DataCols.Contains("_SortKey") Then\par
    dataRowJo("_SortKey") = CInt(dr("_SortKey"))\par
End If\par
Return dataRowJo\par
\par
\cf2 SysFunction\\Sys_DataTable2Json\par
\par
\cf3 Dim dt As DataTable = args(0)\par
Dim typ As Boolean = args(1)\par
\par
Dim dataTableJa As New JArray\par
For i As Integer = 0 To dt.DataRows.Count - 1\par
    dataTableJa.Add(Functions.Execute("Sys_DataRow2JObject", dt.DataRows(i), typ))\par
Next\par
Return dataTableJa\par
\par
\cf2 SysFunction\\Sys_Date2TimeStamp\par
\par
\cf3 '\'b7\'b5\'bb\'d8\'ca\'b1\'bc\'e4\'b4\'c1\par
Dim d As Date = Args(0)\par
Dim typ As String = Args(1)\par
\par
Dim timestamp As Long \par
Select Case typ\par
    Case "ss"\par
        timestamp = New DateTimeOffset(d).ToUnixTimeSeconds()\par
    Case "ms"\par
        timestamp = New DateTimeOffset(d).ToUnixTimeMilliseconds()\par
End Select\par
\par
Return timestamp\par
\par
\cf2 User\\Sys_GetAccessToken\par
\par
\cf3 Dim corpID As String = Args(0)\par
Dim corpSecret As String = Args(1)\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "log"\par
\par
' \'b2\'e9\'d5\'d2\'be\'e0\'c0\'eb\'ca\'a7\'d0\'a710\'b7\'d6\'d6\'d3\'d2\'d4\'c9\'cf\'b5\'c4Token\par
cmd.CommandText = "Select Top 1 AccessTocken From Log4QYWeixinAccessToken Where CorpID = ? And CorpSecret = ? And  DATEDIFF(ss,getDate(), ExpiresDate) > 600"\par
cmd.Parameters.Add("@CorpID", corpID)\par
cmd.Parameters.Add("@CorpSecret", corpSecret)\par
Dim token As String = cmd.ExecuteScalar()\par
cmd.Parameters.Clear()\par
\par
' \'ce\'b4\'d5\'d2\'b5\'bd\'bf\'c9\'d3\'c3Token\'a3\'ac\'d6\'d8\'d0\'c2\'bb\'f1\'c8\'a1Token\par
If token = "" Then\par
    System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12\par
    \par
    Dim url As String = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=\{0\}&corpsecret=\{1\}"\par
    Dim hc As New HttpClient(CExp(url, corpID, corpSecret))\par
    \par
    Dim st As Date = now\par
    Try\par
        Dim ret As String = hc.GetData()\par
        ' \'c8\'e7\'b9\'fb\'ca\'a7\'b0\'dc,\'d4\'d9\'b3\'a2\'ca\'d4\'d2\'bb\'b4\'ce\par
        If ret = "" Then \par
            hc.GetData()\par
        End If\par
        Dim jo As JObject = JObject.Parse(ret)\par
        If jo("access_token") IsNot Nothing Then\par
            token = jo("access_token")\par
            Dim expiresIn As Integer = CInt(jo("expires_in").tostring)\par
            cmd.CommandText = "Insert Into Log4QYWeixinAccessToken (Guid,CorpID,CorpSecret,AccessTocken,ExpiresIn,CreateDate,ExpiresDate) Values (NEWID(),?,?,?,?,?,?)"\par
            cmd.Parameters.Add("@CorpID", corpID)\par
            cmd.Parameters.Add("@CorpSecret", corpSecret)\par
            cmd.Parameters.Add("@AccessTocken", token)\par
            cmd.Parameters.Add("@ExpiresIn", expiresIn)\par
            cmd.Parameters.Add("@CreateDate", st)\par
            cmd.Parameters.Add("@ExpiresDate", st.AddSeconds(expiresIn))\par
            cmd.ExecuteNonQuery\par
            cmd.Parameters.Clear\par
        End If\par
    Catch ex As Exception\par
    End Try\par
End If\par
\par
Return token\par
\par
\cf2 SysFunction\\Sys_JWTDecode\par
\par
\cf3 Dim token As String = Args(0)\par
' \'c9\'e8\'d6\'c3\'c3\'dc\'d4\'bf\par
Dim secretKey As String = "tpit#yxw$666"\par
\par
Dim tokenHandler = New JWT.Builder.JwtBuilder() _\par
.WithAlgorithm(New JWT.Algorithms.HmacSha256Algorithm()) _\par
.WithSecret(secretKey)\par
Dim payload As New JObject\par
Try\par
    payload = tokenHandler.Decode(token, GetType(JObject))\par
Catch ex As Exception\par
    Output.Show(ex.Message)\par
End Try\par
Return payload\par
\par
\cf2 SysFunction\\Sys_JWTTokenCreate\par
\par
\cf3 Dim expMinutes As Integer = Args(0)\par
Dim userGuid As String = Args(1)\par
Dim useId As String = Args(2)\par
Dim userName As String = Args(3)\par
\par
' \'c9\'e8\'d6\'c3\'c3\'dc\'d4\'bf\par
Dim secretKey As String = "tpit#yxw$666"\par
\par
Dim jti As String = Guid.NewGuid.ToString\par
' \'b4\'b4\'bd\'a8 JWT Token\par
Dim token As String = New JWT.Builder.JwtBuilder() _\par
.WithAlgorithm(New JWT.Algorithms.HmacSha256Algorithm()) _\par
.WithSecret(secretKey) _\par
.AddClaim("exp", DateTimeOffset.UtcNow.AddMinutes(expMinutes).ToUnixTimeSeconds()) _\par
.AddClaim("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds()) _\par
.AddClaim("jti", jti) _\par
.AddClaim("sub", userGuid) _\par
.AddClaim("id", useId) _\par
.AddClaim("name", userName) _\par
.Encode()\par
\par
Return token\par
\par
\cf2 SysFunction\\Sys_ReplaceWithInternalParameter\par
\par
\cf3 Dim itemJa As JArray = Args(0)\par
Dim ParameterName As String = Args(1)\par
Dim ParameterType As String = Args(2)\par
Dim ParameterColName As String = Args(3)\par
\par
For i As Integer = 0 To itemJa.Count - 1\par
    Dim dr As DataRow = DataTables("InternalParameters").Find("ParameterType = '" & ParameterType & "' And IsNull(ParameterValue,'') = '" & itemJa(i)(ParameterName).ToString & "'")\par
    If dr IsNot Nothing Then\par
        itemJa(i)(ParameterName) = dr(ParameterColName).ToString\par
    End If\par
Next\par
\par
\cf2 System\\System_AfterOpenProject\par
\par
\cf3 '\'b3\'cc\'d0\'f2\'c6\'f4\'b6\'af\'ba\'f3\'d6\'b4\'d0\'d0\'b4\'cb\'ba\'af\'ca\'fd\par
BaseMainForm.Text = "TPIT WMS Mobile Server"\par
Functions.Execute("System_UpdateSysParameters")\par
Forms("MainFrm").Show()\par
\par
\cf2 System\\System_UpdateSysParameters\par
\par
\cf3 Dim drs As List(Of DataRow) = DataTables("InternalParameters").Select("ParameterType Is Not Null")\par
For Each dr As DataRow In drs\par
    If _sysParms.Property(dr("ParameterType")) Is Nothing Then\par
        _sysParms(dr("ParameterType")) = New JObject\par
    End If\par
    _sysParms(dr("ParameterType"))(dr("ParameterName")) = dr("ParameterValue").Tostring\par
Next\par
\par
\cf2 User\\SystemGetUserInfo\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As New JObject\par
res("data") = resJo\par
\par
' \'bb\'f1\'c8\'a1\'b7\'c3\'ce\'ca\'d3\'c3\'bb\'a7\'c9\'ed\'b7\'dd\par
Dim accessToken As String = Functions.Execute("Sys_GetAccessToken", "wwfe9b2e18dd30144f", "T1xNLyW378yzMt6GU6kAQoSI8z8NDy-URBBaQUZ8b6s")\par
Dim jo As JObject = Functions.Execute("QYWeixinApiGetUserByCode", accessToken, xData("code").Tostring)\par
\par
\par
Dim userID As String\par
\par
If jo.Property("UserId") IsNot Nothing Then\par
    userID = jo("UserId").ToString\par
Else\par
    ' \'b5\'f7\'ca\'d4\'c4\'a3\'ca\'bd\par
    userID = xData("code").Tostring\par
    'res("error") = "\'d3\'c3\'bb\'a7\'c9\'ed\'b7\'dd\'bb\'f1\'c8\'a1\'ca\'a7\'b0\'dc\'a3\'ac\'c7\'eb\'cb\'a2\'d0\'c2\'d6\'d8\'ca\'d4\'a3\'a1"\par
    'Functions.Execute("WebService_APIResult", e, res)\par
    'Return ""\par
End If\par
\par
\par
\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = "sys"\par
\par
' \'cd\'a8\'b9\'fd\'c6\'f3\'d2\'b5\'ce\'a2\'d0\'c5\'b5\'c4UserId\'bb\'f1\'c8\'a1WMS\'d3\'c3\'bb\'a7\'c3\'fb\par
cmd.CommandText = "Select Guid,UserID,UserName From SysUser Where Del = 0 And UserID = ?"\par
cmd.Parameters.Add("@UserID", userID)\par
Dim Values = cmd.ExecuteValues()\par
cmd.Parameters.Clear()\par
If Values.Count = 0 Then\par
    res("error") = "\'b5\'c7\'c8\'eb\'ca\'a7\'b0\'dc\'a3\'ac\'b5\'b1\'c7\'b0\'d4\'b1\'b9\'a4\'b7\'c7WMS\'cf\'b5\'cd\'b3\'ca\'da\'c8\'a8\'d3\'c3\'bb\'a7!"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
\par
resJo("userName") = Values("UserName").ToString\par
resJo("expiresIn") = 7200\par
' \'c9\'fa\'b3\'c9\'d3\'c3\'bb\'a7token\par
resJo("accessToken") = Functions.Execute("Sys_JWTTokenCreate", 120, Values("Guid"), Values("UserID"), Values("UserName")).ToString\par
\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 User\\SystemRefreshToken\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
'Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
\par
Dim res As New JObject\par
Dim resJo As New JObject\par
res("data") = resJo'\par
\par
' \'bb\'f1\'c8\'a1Token\par
Dim authHeader As String = e.Headers("Authorization")\par
If authHeader.StartsWith("Bearer ") Then\par
    Dim token As String = authHeader.Substring("Bearer ".Length).Trim()\par
    Dim jwtUser As JObject = Functions.Execute("Sys_JWTDecode", token)\par
    \par
    ' \'c9\'fa\'b3\'c9\'d0\'c2Token\par
    resJo("userName") = jwtUser.Value(Of String)("name")\par
    resJo("expiresIn") = 7200\par
    resJo("accessToken") = Functions.Execute("Sys_JWTTokenCreate", 120, jwtUser.Value(Of String)("sub"), jwtUser.Value(Of String)("id"), jwtUser.Value(Of String)("name")).ToString\par
Else\par
    e.StatusCode = 401\par
    res("error") = "Token\'cb\'a2\'d0\'c2\'ca\'a7\'b0\'dc\'a3\'a1"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    Return ""\par
End If\par
\par
Functions.Execute("WebService_APIResult", e, res)\par
\par
\cf2 WebServices\\WebService_APIResult\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim res As JObject = Args(1)\par
\par
'\'b8\'b3\'d6\'b5\'b7\'b5\'bb\'d8\'ca\'b1\'bc\'e4\'b4\'c1\par
Dim timestamp As Long = Functions.Execute("Sys_Date2TimeStamp", Date.Now, "ms")\par
\par
If res.Property("error") Is Nothing Then\par
    res("code") = 0\par
    res("message") = "success"\par
    If res.Property("data") Is Nothing Then\par
        res("data") = ""\par
    End If\par
Else\par
    If e.StatusCode = 200 Then\par
        e.StatusCode = 400\par
    End If \par
    res("code") = -1\par
    res("message") = res("error").ToString\par
End If \par
\par
Output.Show(res.ToString)\par
\par
e.WriteString(CompressJson(res))\par
\par
'\'c8\'f4\'ca\'c7\'d2\'ec\'b2\'bdAPI,\'b9\'d8\'b1\'d5\'d0\'c5\'b5\'c0\par
If e.AsyncExecute Then\par
    e.Handled = True\par
End If\par
\par
\cf2 WebServices\\WebService_BeforeRequest\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Output.Show("\'b7\'c3\'ce\'ca\'c2\'b7\'be\'b6:" & e.path)\par
Output.Show(e.Request.HttpMethod.Tostring)\par
\par
Dim res As New JObject\par
Dim authStatus As Boolean = True\par
\par
'\'b2\'e9\'d1\'afAPI\'d0\'c5\'cf\'a2\par
Dim dr As DataRow = DataTables("InternalAPI").Find("IsAbandoned = 0 And AccessPath = '" & e.path & "' And HttpMethod = '" & e.Request.HttpMethod.Tostring & "'")\par
\par
If dr Is Nothing Then\par
    e.StatusCode = 404\par
    res("error") = "API Not Found"\par
    Functions.Execute("WebService_APIResult", e, res)\par
    e.Cancel = True\par
    Return ""\par
End If\par
\par
If dr("IsAuthRequired") Then\par
    Dim authHeader As String = e.Headers("Authorization")\par
    If authHeader = "" Then\par
        res("error") = "Authorization Error"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        e.Cancel = True\par
        Return ""\par
    End If \par
    If authHeader.StartsWith("Bearer ") Then\par
        Dim token As String = authHeader.Substring("Bearer ".Length).Trim()\par
        Dim jwtUser As JObject = Functions.Execute("Sys_JWTDecode", token)\par
        If DateTimeOffset.UtcNow.ToUnixTimeSeconds() > val(jwtUser.Value(Of String)("exp")) + 30 Then\par
            authStatus = False\par
        End If \par
        \par
        'Output.Show(DateTimeOffset.UtcNow.ToUnixTimeSeconds())\par
        'Output.Show(val(jwtUser.Value(Of String)("exp")))\par
    End If\par
    \par
    Output.Show(authStatus)\par
    If authStatus = False Then\par
        e.StatusCode = 401\par
        res("error") = "Auth Error"\par
        Functions.Execute("WebService_APIResult", e, res)\par
        e.Cancel = True\par
    End If\par
Else\par
    Dim sb As New StringBuilder\par
    For Each key As String In e.Cookies.Keys\par
        sb.AppendLine(key & ":" & e.Cookies(key))\par
    Next\par
    Output.Show(sb.Tostring)\par
End If\par
\par
\cf2 WebServices\\WebService_BuildConditionStr\par
\par
\cf3 Dim path As String = Args(0)\par
Dim xData As JToken = Args(1)\par
\par
Dim str As String = ""\par
Dim sort As String = ""\par
\par
Dim buildData As JObject = JObject.Parse(xData.Tostring)\par
If buildData.Property("sort") IsNot Nothing Then\par
    sort = buildData("sort").ToString\par
End If\par
Dim drs As List(Of DataRow) = DataTables("InternalApiCondition").Select("ApiPath = '" & path & "'")\par
For Each dr As DataRow In drs\par
    If buildData.Property(dr("WordName")) Is Nothing Then\par
        Continue For\par
    End If\par
    '\'bb\'f1\'c8\'a1\'cc\'e1\'bd\'bb\'d6\'b5\par
    Dim val As String = buildData(dr("WordName")).Tostring\par
    '\'d7\'a2\'c8\'eb\'b4\'a6\'c0\'ed\par
    val = ReplaceSqlChar(val)\par
    '\'c5\'d0\'b6\'cfWordType\par
    Select Case dr("WordType")\par
        Case "TextEx"\par
            Try\par
                Dim textJo As JObject = JObject.Parse(val)\par
                Dim text As String = textJo("text").ToString\par
                Select Case textJo("type").ToString\par
                    Case "0"\par
                        str = str & " And " & dr("MappingField") & " = '" & text & "'"\par
                    Case "1"\par
                        str = str & " And " & dr("MappingField") & " Like '%" & text & "%'"\par
                    Case "2"\par
                        str = str & " And " & dr("MappingField") & " <> '" & text & "'"\par
                    Case "3"\par
                        str = str & " And (" & dr("MappingField") & " Is Null Or " & dr("MappingField") & " = '')"\par
                    Case "4"\par
                        str = str & " And " & dr("MappingField") & " In ('" & text.replace(",", "','") & "')"\par
                End Select\par
                \par
            Catch ex As Exception \par
            End Try\par
        Case "Text"\par
            '\'c5\'d0\'b6\'cfOperator\par
            Select Case dr("Operator") \par
                '\'ce\'c4\'b1\'be\'b2\'e9\'d1\'af - \'b5\'c8\'d3\'da\'a3\'a8\'b6\'e0\'d6\'b5\'d3\'c3|\'b7\'d6\'b8\'ee\'a3\'a9\par
                Case "Equal"\par
                    Dim strequal As String = ""\par
                    Dim Values() As String\par
                    Values = dr("MappingField").split("|")\par
                    For Index As Integer = 0 To Values.Length - 1\par
                        If strequal = "" Then\par
                            strequal = Values(Index) & " = '" & val & "'"\par
                        Else\par
                            strequal = strequal & " Or " & Values(Index) & " = '" & val & "'"\par
                        End If\par
                    Next\par
                    str = str & " And (" & strequal & ")"\par
                    '\'ce\'c4\'b1\'be\'b2\'e9\'d1\'af - \'b2\'bb\'b5\'c8\'d3\'da\par
                Case "NotEqual"\par
                    str = str & " And " & dr("MappingField") & " <> '" & val & "'"\par
                    '\'ce\'c4\'b1\'be\'b2\'e9\'d1\'af - \'c4\'a3\'ba\'fd\'b2\'e9\'d1\'af\'a3\'a8\'b6\'e0\'d6\'b5\'d3\'c3|\'b7\'d6\'b8\'ee\'a3\'a9\par
                Case "Like"\par
                    Dim strlike As String = ""\par
                    Dim Values() As String\par
                    Values = dr("MappingField").split("|")\par
                    For Index As Integer = 0 To Values.Length - 1\par
                        If strlike = "" Then\par
                            strlike = Values(Index) & " Like '%" & val & "%'"\par
                        Else\par
                            strlike = strlike & "Or " & Values(Index) & " Like '%" & val & "%'"\par
                        End If\par
                    Next\par
                    str = str & " And (" & strlike & ")"\par
                    '\'ce\'c4\'b1\'be\'b2\'e9\'d1\'af - \'ca\'c7\'b7\'f1\'ce\'aa\'bf\'d5\'a3\'a80\'bf\'d5|1\'b7\'c7\'bf\'d5\'a3\'a9\par
                Case "IsNull"\par
                    If val = 0 Then\par
                        str = str & " And " & dr("MappingField") & " Is Null"\par
                    ElseIf val = 1 Then\par
                        str = str & " And " & dr("MappingField") & " Is Not Null"\par
                    End If\par
            End Select\par
            '\'c8\'d5\'c6\'da\'b7\'b6\'ce\'a7\'b2\'e9\'d1\'af\par
        Case "DateRange"\par
            Try\par
                Dim rangeJo As JObject = JObject.Parse(val)\par
                If rangeJo("type").tostring = 1 Then\par
                    str = str & " And " & dr("MappingField") & " Is Null"\par
                Else\par
                    If rangeJo("date").tostring = "" OrElse rangeJo("date").tostring = "[]" Then\par
                        str = str & " And " & dr("MappingField") & " Is Not Null"\par
                    Else \par
                        str = str & " And " & dr("MappingField") & " >= '" & Format(CDate(rangeJo("date")(0).Tostring), "yyyy-MM-dd") & "' And " & dr("MappingField") & "< '" & Format(CDate(rangeJo("date")(1).Tostring).AddDays(1), "yyyy-MM-dd") & "'"\par
                    End If \par
                End If \par
            Catch ex As Exception \par
            End Try\par
        Case "NumberRange"\par
            Try\par
                Dim rangeJo As JObject = JObject.Parse(val)\par
                If rangeJo("type").tostring = 2 Then\par
                    str = str & " And " & dr("MappingField") & " = 0"\par
                ElseIf rangeJo("type").tostring = 1 Then\par
                    str = str & " And " & dr("MappingField") & " <> 0"\par
                Else\par
                    If rangeJo.Property("num1") IsNot Nothing AndAlso rangeJo.Property("num2") IsNot Nothing Then\par
                        str = str & " And " & dr("MappingField") & " >= " & rangeJo("num1").ToString & " And " & dr("MappingField") & "<= " & rangeJo("num2").ToString\par
                    End If \par
                End If \par
            Catch ex As Exception \par
            End Try\par
            '\'b6\'e0\'cf\'ee\'d1\'a1\'d4\'f1\'b2\'e9\'d1\'af\par
        Case "MultText"\par
            str = str & " And " & dr("MappingField") & " In ('" & val.replace(",", "','") & "')"\par
            '\'ca\'fd\'d6\'b5\'b2\'e9\'d1\'af\par
        Case "Int"\par
            If dr("Mark") = "Gt" Then\par
                str = str & " And " & dr("MappingField") & " > " & CInt(val)\par
            ElseIf dr("Mark") = "Lt" Then\par
                str = str & " And " & dr("MappingField") & " < " & CInt(val)\par
            End If \par
    End Select\par
    \par
Next\par
Output.Show(str)\par
Return str\par
\par
\cf2 WebServices\\WebService_GetDataTableWithPagingLimits\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim connectName As String = Args(1)\par
Dim viewName As String = Args(2)\par
Dim field As String = Args(3)\par
Dim extCondition As String = Args(4)\par
Dim typ As Boolean = Args(5)\par
\par
\par
Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
Output.Show(xData.ToString)\par
'\'ba\'cf\'b3\'c9\'b2\'e9\'d1\'af\'cc\'f5\'bc\'fe\par
Dim conditionStr As String = Functions.Execute("WebService_BuildConditionStr", e.path, xData)\par
conditionStr = conditionStr & " " & extCondition\par
\par
Dim resJo As New JObject\par
Dim cmd As New SQLCommand\par
cmd.ConnectionName = connectName\par
\par
'\'b0\'b4\'cc\'f5\'bc\'fe\'b2\'e9\'d1\'af\'ca\'fd\'be\'dd\'d7\'dc\'cc\'f5\'ca\'fd\par
cmd.CommandText = "Select Count(_Identify) As Total From " & viewName & " Where 1=1 " & conditionStr\par
resJo("total") = CInt(cmd.ExecuteScalar())\par
\par
'\'b0\'b4\'cc\'f5\'bc\'fe\'b7\'d6\'d2\'b3\'b2\'e9\'d1\'af\par
cmd.CommandText = "SELECT \{0\} FROM \{1\} WHERE 1=1 \{2\} ORDER BY \{3\} OFFSET \{4\} ROWS FETCH NEXT \{5\} ROWS ONLY"\par
cmd.CommandText = CExp(cmd.CommandText, field, viewName, conditionStr, xData("sort").ToString, (CInt(xData("currentPage").Tostring) - 1) * CInt(xData("pageSize").Tostring), CInt(xData("pageSize").Tostring))\par
Output.Show(cmd.CommandText)\par
Dim dt As DataTable = cmd.ExecuteReader\par
resJo("items") = Functions.Execute("Sys_DataTable2Json", dt, typ)\par
\par
Return resJo\par
\par
\cf2 WebServices\\WebService_GetJWTUser\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim field As String = Args(1)\par
Dim authHeader As String = e.Headers("Authorization")\par
\par
If authHeader.StartsWith("Bearer ") Then\par
    Dim token As String = authHeader.Substring("Bearer ".Length).Trim()\par
    Dim jwtUser As JObject = Functions.Execute("Sys_JWTDecode", token)\par
    Return jwtUser.Value(Of String)(field)\par
Else\par
    Return ""\par
End If\par
\par
\cf2 WebServices\\WebService_GetParameters\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim xData As New JObject\par
Select Case e.Request.HttpMethod.Tostring\par
    Case "POST"\par
        xData = JObject.Parse(e.PlainText)\par
    Case "GET"\par
        For Each key As String In e.GetValues.Keys\par
            xData(key) = e.GetValues(key)\par
        Next \par
End Select\par
\par
Return xData\par
\par
\cf2 WebServices\\WebService_Main\par
\par
\cf3 Dim e As RequestEventArgs = Args(0)\par
Dim st As Date = Date.Now\par
Dim res As New JObject\par
Dim verification As Boolean = True\par
Output.Show(e.Headers.ToString)\par
\par
'\'b2\'e9\'d1\'afAPI\'d0\'c5\'cf\'a2\par
Dim dr As DataRow = DataTables("InternalAPI").Find("AccessPath = '" & e.path & "' And HttpMethod = '" & e.Request.HttpMethod.Tostring & "'")\par
Dim parmJa As New JArray\par
parmJa = Jarray.Parse(dr("Parameters"))\par
If parmJa.Count > 0 Then\par
    '\'d0\'a3\'d1\'e9\'b2\'ce\'ca\'fd\par
    Dim xData As JObject = Functions.Execute("WebService_GetParameters", e)\par
    Output.Show(xData.ToString)\par
    For i As Integer = 0 To parmJa.count - 1\par
        If xData.Property(parmJa(i)) Is Nothing Then\par
            res("error") = "\'b7\'c3\'ce\'ca\'b2\'ce\'ca\'fd\'d2\'ec\'b3\'a3!"\par
            Functions.Execute("WebService_APIResult", e, res)\par
            Return ""\par
        End If\par
    Next\par
End If\par
\par
If verification AndAlso dr("MappingFunction") <> "" Then\par
    '\'d6\'b4\'d0\'d0API\'b6\'d4\'d3\'a6\'b5\'c4\'ba\'af\'ca\'fd\par
    If dr("IsAsyncFunc") Then\par
        e.AsyncExecute = True\par
        Functions.AsyncExecute(dr("MappingFunction"), e)\par
    Else\par
        Functions.Execute(dr("MappingFunction"), e)\par
    End If\par
Else\par
    res("message") = "\'b7\'c3\'ce\'ca\'d2\'ec\'b3\'a3,\'c7\'eb\'cb\'a2\'d0\'c2\'ba\'f3\'d6\'d8\'ca\'d4"\par
    'Functions.Execute("ApiResult", e, res)\par
    Return ""\par
End If\par
\par
\cf1\'c8\'ab\'be\'d6\'b4\'fa\'c2\'eb\par
\par
\cf2 Default\par
\par
\cf3 'Use Ref Struct In VB.NET\par
 < Obsolete("Allow ref structs in VB.NET", False) > \par
Sub main()\par
End Sub\par
\par
'\'b6\'a8\'d2\'e5\'c8\'ab\'be\'d6\'b1\'e4\'c1\'bf\par
Public _sysParms As New JObject\par
\par
\cf2 ReplaceSqlChar\par
\par
\cf3 '\'ce\'c4\'b1\'be\'cc\'e6\'bb\'bb,\'b1\'dc\'c3\'e2SQL\'d7\'a2\'c8\'eb\par
Public Function ReplaceSQLChar(ByVal str As String) As String\par
    If str <> "" Then\par
        str = str.Replace("'", "''")\par
        str = str.Replace("\rquote ", "''")\par
        str = str.Replace("\lquote ", "''")\par
        str = str.Replace(";", "")\par
        str = str.Replace("?", "")\par
        str = str.Replace("<", "")\par
        str = str.Replace(">", "")\par
        str = str.Replace("(", "")\par
        str = str.Replace(")", "")\par
        str = str.Replace("@", "")\par
        str = str.Replace("=", "")\par
        str = str.Replace("+", "")\par
        str = str.Replace("*", "")\par
        str = str.Replace("#", "")\par
        str = str.Replace("%", "")\par
        str = str.Replace("$", "")\par
        str = Regex.Replace(str, "select ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "insert ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "delete from", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "count ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "drop table", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "truncate ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "asc ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "mid ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "char ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "xp_cmdshell", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "exec master", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "net localgroup administrators", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "net user", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "net ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "delete ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "drop ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "script ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "update ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, " and ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, " or ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, " chr ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "master ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "truncate ", "", RegexOptions.IgnoreCase)\par
        str = Regex.Replace(str, "declare ", "", RegexOptions.IgnoreCase)\par
    End If\par
    Return str.Trim()\par
End Function\par
\par
\cf2 GS1\par
\par
\cf3 ' \'d3\'c3\'d3\'da\'b4\'e6\'b4\'a2\'c3\'bf\'b8\'f6AI\'b5\'c4\'b6\'a8\'d2\'e5\'d0\'c5\'cf\'a2\par
Public Class AIInfo\par
    Public Property AI As String\par
    Public Property Description As String\par
    Public Property IsVariableLength As Boolean\par
    ' \'ca\'fd\'be\'dd\'d7\'d6\'b6\'ce\'b5\'c4\'b3\'a4\'b6\'c8 (\'b6\'d4\'d3\'da\'b9\'cc\'b6\'a8\'b3\'a4\'b6\'c8AI)\par
    Public Property FixedDataLength As Integer\par
    ' \'ca\'fd\'be\'dd\'d7\'d6\'b6\'ce\'b5\'c4\'d7\'ee\'b4\'f3\'b3\'a4\'b6\'c8 (\'b6\'d4\'d3\'da\'bf\'c9\'b1\'e4\'b3\'a4\'b6\'c8AI)\par
    Public Property MaxDataLength As Integer\par
    ' \'ca\'fd\'be\'dd\'c0\'e0\'d0\'cd\'a3\'ac\'bf\'c9\'d3\'c3\'d3\'da\'ba\'f3\'d0\'f8\'b4\'a6\'c0\'ed (\'c8\'e7\'c8\'d5\'c6\'da\'a1\'a2\'ca\'fd\'d7\'d6\'b5\'c8)\par
    Public Property DataType As String\par
\par
    Public Sub New(ai As String, description As String, isVariable As Boolean, dataLength As Integer, maxDataLength As Integer, dataType As String)\par
        Me.AI = ai\par
        Me.Description = description\par
        Me.IsVariableLength = isVariable\par
        Me.FixedDataLength = dataLength\par
        Me.MaxDataLength = maxDataLength\par
        Me.DataType = dataType\par
    End Sub\par
End Class\par
\par
\par
' \'d3\'c3\'d3\'da\'b4\'e6\'b4\'a2\'bd\'e2\'ce\'f6\'ba\'f3\'b5\'c4\'b5\'a5\'b8\'f6\'d4\'aa\'cb\'d8\par
Public Class ParsedElement\par
    Public Property AI As AIInfo\par
    Public Property Value As String\par
\par
    Public Overrides Function ToString() As String\par
        Return Value\par
    End Function\par
End Class\par
\par
\cf2 GS1128Parser\par
\par
\cf3 Public Class GS1128Parser\par
    Private Shared ReadOnly AIDictionary As New Dictionary(Of String, AIInfo) From \{\par
    \{"00", New AIInfo("00", "Serial Shipping Container Code (SSCC)", False, 18, 18, "N")\}, \par
    \{"01", New AIInfo("01", "Global Trade Item Number (GTIN)", False, 14, 14, "N")\}, \par
    \{"02", New AIInfo("02", "GTIN of Contained Trade Items", False, 14, 14, "N")\}, \par
    \{"10", New AIInfo("10", "Batch or Lot Number", False, 6, 6, "N")\}, \par
    \{"11", New AIInfo("11", "Production Date (YYMMDD)", False, 6, 6, "Date")\}, \par
    \{"13", New AIInfo("13", "Packaging Date (YYMMDD)", False, 6, 6, "Date")\}, \par
    \{"15", New AIInfo("15", "Best Before Date (YYMMDD)", False, 6, 6, "Date")\}, \par
    \{"17", New AIInfo("17", "Expiration Date (YYMMDD)", False, 6, 6, "Date")\}, \par
    \{"20", New AIInfo("20", "Internal Product Variant", False, 2, 2, "N")\}\par
    \}\par
    \par
    Public Function Parse(ByVal rawBarcode As String) As List(Of ParsedElement)\par
        Dim results As New List(Of ParsedElement)\par
        Dim currentIndex As Integer = 0\par
\par
        While currentIndex < rawBarcode.Length\par
            ' \'b2\'e9\'d5\'d2\'c6\'a5\'c5\'e4\'b5\'c4AI\par
            Dim foundAI As AIInfo = FindAIAtPosition(rawBarcode, currentIndex)\par
            \par
            If foundAI Is Nothing Then\par
                ' \'c8\'e7\'b9\'fb\'d4\'da\'b5\'b1\'c7\'b0\'ce\'bb\'d6\'c3\'d5\'d2\'b2\'bb\'b5\'bd\'d3\'d0\'d0\'a7\'b5\'c4AI\'a3\'ac\'cb\'b5\'c3\'f7\'cc\'f5\'c2\'eb\'b8\'f1\'ca\'bd\'b4\'ed\'ce\'f3\par
                Throw New FormatException("Invalid GS1-128 format")\par
            End If\par
            ' \'d2\'c6\'b6\'af\'d6\'b8\'d5\'eb\'b5\'bd\'ca\'fd\'be\'dd\'bf\'aa\'ca\'bc\'b5\'c4\'ce\'bb\'d6\'c3\par
            currentIndex += foundAI.AI.Length\par
            \par
            Dim dataLength As Integer = 0\par
            If foundAI.IsVariableLength Then\par
                ' --- \'b4\'a6\'c0\'ed\'bf\'c9\'b1\'e4\'b3\'a4\'b6\'c8AI ---\par
                ' \'b2\'e9\'d5\'d2\'cf\'c2\'d2\'bb\'b8\'f6AI\'b5\'c4\'ce\'bb\'d6\'c3\'a3\'ac\'d2\'d4\'c8\'b7\'b6\'a8\'b5\'b1\'c7\'b0\'ca\'fd\'be\'dd\'b5\'c4\'bd\'e1\'ca\'f8\'ce\'bb\'d6\'c3\par
                Dim nextAIPosition As Integer = FindNextAIPosition(rawBarcode, currentIndex, foundAI.MaxDataLength)\par
                \par
                If nextAIPosition = -1 Then\par
                    ' \'c3\'bb\'d3\'d0\'d5\'d2\'b5\'bd\'cf\'c2\'d2\'bb\'b8\'f6AI\'a3\'ac\'cb\'b5\'c3\'f7\'b5\'b1\'c7\'b0\'ca\'fd\'be\'dd\'d6\'b1\'b5\'bd\'d7\'d6\'b7\'fb\'b4\'ae\'c4\'a9\'ce\'b2\par
                    dataLength = rawBarcode.Length - currentIndex\par
                Else\par
                    dataLength = nextAIPosition - currentIndex\par
                End If\par
                \par
                ' \'bc\'ec\'b2\'e9\'ca\'fd\'be\'dd\'b3\'a4\'b6\'c8\'ca\'c7\'b7\'f1\'b3\'ac\'b9\'fd\'c1\'cb\'b8\'c3AI\'d4\'ca\'d0\'ed\'b5\'c4\'d7\'ee\'b4\'f3\'b3\'a4\'b6\'c8\par
                If dataLength > foundAI.MaxDataLength Then\par
                    Throw New FormatException("Data for AI exceeds max length")\par
                End If\par
            Else\par
                ' --- \'b4\'a6\'c0\'ed\'b9\'cc\'b6\'a8\'b3\'a4\'b6\'c8AI ---\par
                dataLength = foundAI.FixedDataLength\par
            End If\par
            \par
            ' \'bc\'ec\'b2\'e9\'ca\'a3\'d3\'e0\'d7\'d6\'b7\'fb\'b4\'ae\'ca\'c7\'b7\'f1\'d7\'e3\'b9\'bb\'b3\'a4\par
            If currentIndex + dataLength > rawBarcode.Length Then\par
                Throw New FormatException("Insufficient data for AI: " & foundAI.AI & "Expected " & dataLength & " characters, but only " & rawBarcode.Length - currentIndex & " remain.")\par
            End If\par
            \par
            ' \'cc\'e1\'c8\'a1\'ca\'fd\'be\'dd\par
            Dim dataValue As String = rawBarcode.Substring(currentIndex, dataLength)\par
            \par
            ' \'b4\'b4\'bd\'a8\'b2\'a2\'cc\'ed\'bc\'d3\'bd\'e2\'ce\'f6\'bd\'e1\'b9\'fb\par
            results.Add(New ParsedElement() With \{\par
            .AI = foundAI, \par
            .Value = dataValue\par
            \})\par
            \par
            ' \'b8\'fc\'d0\'c2\'d6\'b8\'d5\'eb\'b5\'bd\'cf\'c2\'d2\'bb\'b8\'f6AI\'b5\'c4\'bf\'aa\'ca\'bc\'ce\'bb\'d6\'c3\par
            currentIndex += dataLength\par
        End While\par
        \par
        Return results\par
    End Function\par
    \par
    Private Function FindAIAtPosition(ByVal barcode As String, ByVal position As Integer) As AIInfo\par
        ' \'d3\'c5\'cf\'c8\'bc\'ec\'b2\'e94\'ce\'bbAI\par
        If position + 4 <= barcode.Length Then\par
            Dim potentialAI4 As String = barcode.Substring(position, 4)\par
            If AIDictionary.ContainsKey(potentialAI4) Then\par
                Return AIDictionary(potentialAI4)\par
            End If\par
        End If\par
        \par
        ' \'c6\'e4\'b4\'ce\'bc\'ec\'b2\'e93\'ce\'bbAI\par
        If position + 3 <= barcode.Length Then\par
            Dim potentialAI3 As String = barcode.Substring(position, 3)\par
            If AIDictionary.ContainsKey(potentialAI3) Then\par
                Return AIDictionary(potentialAI3)\par
            End If\par
        End If\par
        \par
        ' \'d7\'ee\'ba\'f3\'bc\'ec\'b2\'e92\'ce\'bbAI\par
        If position + 2 <= barcode.Length Then\par
            Dim potentialAI2 As String = barcode.Substring(position, 2)\par
            If AIDictionary.ContainsKey(potentialAI2) Then\par
                Return AIDictionary(potentialAI2)\par
            End If\par
        End If\par
        \par
        Return Nothing ' \'ce\'b4\'d5\'d2\'b5\'bd\par
    End Function\par
    \par
    Private Function FindNextAIPosition(ByVal barcode As String, ByVal startSearchPos As Integer, ByVal maxDataLength As Integer) As Integer\par
        Dim searchLimit = Math.Min(startSearchPos + maxDataLength, barcode.Length)\par
        \par
        For i As Integer = startSearchPos To searchLimit - 1\par
            If FindAIAtPosition(barcode, i) IsNot Nothing Then\par
                Return i ' \'d5\'d2\'b5\'bd\'c1\'cb\'cf\'c2\'d2\'bb\'b8\'f6AI\'b5\'c4\'c6\'f0\'ca\'bc\'ce\'bb\'d6\'c3\par
            End If\par
        Next\par
        \par
        Return - 1 ' \'ce\'b4\'d5\'d2\'b5\'bd\'cf\'c2\'d2\'bb\'b8\'f6AI\par
    End Function\par
End Class\par
\par
\cf1\'b2\'cb\'b5\'a5\'ca\'c2\'bc\'fe\par
\par
\cf0\par
}
 