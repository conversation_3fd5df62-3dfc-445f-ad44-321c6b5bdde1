using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMSApiCore.Models.Entities;

/// <summary>
/// 内部路由表
/// </summary>
[Table("InternalRouter")]
public class InternalRouter
{
    /// <summary>
    /// 主键GUID
    /// </summary>
    [Key]
    [StringLength(36)]
    public string Guid { get; set; } = string.Empty;

    /// <summary>
    /// 路由名称
    /// </summary>
    [StringLength(32)]
    public string RouteName { get; set; } = string.Empty;

    /// <summary>
    /// 路径
    /// </summary>
    [StringLength(64)]
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// 重定向
    /// </summary>
    [StringLength(64)]
    public string Redirect { get; set; } = string.Empty;

    /// <summary>
    /// 组件
    /// </summary>
    [StringLength(128)]
    public string Component { get; set; } = string.Empty;

    /// <summary>
    /// 层级
    /// </summary>
    public int Level { get; set; } = 0;

    /// <summary>
    /// 父级名称
    /// </summary>
    [StringLength(24)]
    public string ParentName { get; set; } = string.Empty;

    /// <summary>
    /// 元数据激活图标
    /// </summary>
    [StringLength(32)]
    public string MetaActiveIcon { get; set; } = string.Empty;

    /// <summary>
    /// 元数据激活路径
    /// </summary>
    [StringLength(64)]
    public string MetaActivePath { get; set; } = string.Empty;

    /// <summary>
    /// 元数据隐藏子菜单
    /// </summary>
    public bool MetaHideChildrenInMenu { get; set; } = false;

    /// <summary>
    /// 元数据隐藏面包屑
    /// </summary>
    public bool MetaHideInBreadcrumb { get; set; } = false;

    /// <summary>
    /// 元数据隐藏菜单
    /// </summary>
    public bool MetaHideInMenu { get; set; } = false;

    /// <summary>
    /// 元数据隐藏标签页
    /// </summary>
    public bool MetaHideInTab { get; set; } = false;

    /// <summary>
    /// 元数据图标
    /// </summary>
    [StringLength(40)]
    public string MetaIcon { get; set; } = string.Empty;

    /// <summary>
    /// 元数据保持活跃
    /// </summary>
    public bool MetaKeepAlive { get; set; } = false;

    /// <summary>
    /// 元数据链接
    /// </summary>
    [StringLength(255)]
    public string MetaLink { get; set; } = string.Empty;

    /// <summary>
    /// 元数据排序
    /// </summary>
    public int MetaOrder { get; set; } = 0;

    /// <summary>
    /// 元数据标题
    /// </summary>
    [StringLength(20)]
    public string MetaTitle { get; set; } = string.Empty;

    /// <summary>
    /// 是否删除
    /// </summary>
    public bool Del { get; set; } = false;

    /// <summary>
    /// 元数据固定标签页
    /// </summary>
    public bool MetaAffixTab { get; set; } = false;
}
