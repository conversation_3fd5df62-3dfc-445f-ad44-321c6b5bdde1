using System.Text;
using System.Text.Json;
using WMSApiCore.Services;
using WMSApiCore.Models.DTOs;

namespace WMSApiCore.Middleware;

/// <summary>
/// 动态API中间件
/// </summary>
public class DynamicApiMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<DynamicApiMiddleware> _logger;

    public DynamicApiMiddleware(RequestDelegate next, ILogger<DynamicApiMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var path = context.Request.Path.Value;
        var method = context.Request.Method;

        // 跳过静态文件和系统路径
        if (ShouldSkipPath(path))
        {
            await _next(context);
            return;
        }

        try
        {
            var cacheService = context.RequestServices.GetRequiredService<MemoryCacheService>();
            var functionService = context.RequestServices.GetRequiredService<FunctionExecutionService>();

            // 查找API映射
            var apiMapping = await cacheService.GetAPIByPathAsync(path!, method);
            if (apiMapping == null)
            {
                await _next(context);
                return;
            }

            _logger.LogInformation("处理动态API请求: {Method} {Path} -> {Function}", 
                method, path, apiMapping.MappingFunction);

            // 检查认证
            if (apiMapping.IsAuthRequired && !await IsAuthenticatedAsync(context))
            {
                await WriteErrorResponse(context, 401, "未授权访问");
                return;
            }

            // 获取请求参数
            var parameters = await GetRequestParametersAsync(context);

            // 执行映射函数
            var result = await functionService.ExecuteAsync(apiMapping.MappingFunction, context, parameters);

            // 返回结果
            await WriteJsonResponse(context, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理动态API请求时发生错误: {Method} {Path}", method, path);
            await WriteErrorResponse(context, 500, "服务器内部错误");
        }
    }

    /// <summary>
    /// 判断是否应该跳过的路径
    /// </summary>
    /// <param name="path">请求路径</param>
    /// <returns>是否跳过</returns>
    private static bool ShouldSkipPath(string? path)
    {
        if (string.IsNullOrEmpty(path))
            return true;

        var skipPaths = new[]
        {
            "/swagger",
            "/openapi",
            "/favicon.ico",
            "/_framework",
            "/css",
            "/js",
            "/images",
            "/lib"
        };

        return skipPaths.Any(skipPath => path.StartsWith(skipPath, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 检查用户是否已认证
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <returns>是否已认证</returns>
    private async Task<bool> IsAuthenticatedAsync(HttpContext context)
    {
        // 检查Authorization头
        if (!context.Request.Headers.ContainsKey("Authorization"))
            return false;

        var authHeader = context.Request.Headers["Authorization"].ToString();
        if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            return false;

        try
        {
            var token = authHeader.Substring("Bearer ".Length).Trim();
            var jwtService = context.RequestServices.GetRequiredService<JwtService>();
            var principal = await jwtService.ValidateTokenAsync(token);
            
            if (principal != null)
            {
                context.User = principal;
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "JWT令牌验证失败");
        }

        return false;
    }

    /// <summary>
    /// 获取请求参数
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <returns>参数字典</returns>
    private async Task<Dictionary<string, object>> GetRequestParametersAsync(HttpContext context)
    {
        var parameters = new Dictionary<string, object>();

        // 获取查询参数
        foreach (var query in context.Request.Query)
        {
            parameters[query.Key] = query.Value.ToString();
        }

        // 获取表单参数
        if (context.Request.HasFormContentType)
        {
            var form = await context.Request.ReadFormAsync();
            foreach (var formItem in form)
            {
                parameters[formItem.Key] = formItem.Value.ToString();
            }
        }

        // 获取JSON参数
        if (context.Request.ContentType?.Contains("application/json") == true)
        {
            context.Request.EnableBuffering();
            context.Request.Body.Position = 0;
            
            using var reader = new StreamReader(context.Request.Body, Encoding.UTF8, leaveOpen: true);
            var body = await reader.ReadToEndAsync();
            
            if (!string.IsNullOrEmpty(body))
            {
                try
                {
                    var jsonData = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(body);
                    if (jsonData != null)
                    {
                        foreach (var item in jsonData)
                        {
                            parameters[item.Key] = item.Value.ToString();
                        }
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "解析JSON参数失败: {Body}", body);
                }
            }
            
            context.Request.Body.Position = 0;
        }

        return parameters;
    }

    /// <summary>
    /// 写入JSON响应
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="data">响应数据</param>
    private async Task WriteJsonResponse(HttpContext context, object data)
    {
        context.Response.ContentType = "application/json; charset=utf-8";
        
        var json = JsonSerializer.Serialize(data, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });
        
        await context.Response.WriteAsync(json);
    }

    /// <summary>
    /// 写入错误响应
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="statusCode">状态码</param>
    /// <param name="message">错误消息</param>
    private async Task WriteErrorResponse(HttpContext context, int statusCode, string message)
    {
        context.Response.StatusCode = statusCode;
        
        var errorResponse = new ApiResponse<object>
        {
            Success = false,
            Message = message,
            Data = null
        };
        
        await WriteJsonResponse(context, errorResponse);
    }
}
