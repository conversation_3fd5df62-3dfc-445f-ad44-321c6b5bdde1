using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using WMSApiCore.Data;
using WMSApiCore.Models.DTOs;
using WMSApiCore.Models.Entities;

namespace WMSApiCore.Services.Business;

/// <summary>
/// 认证业务服务
/// </summary>
public class AuthService
{
    private readonly WMSDbContext _dbContext;
    private readonly JwtService _jwtService;
    private readonly EncryptionService _encryptionService;
    private readonly MemoryCacheService _cacheService;
    private readonly ILogger<AuthService> _logger;

    public AuthService(
        WMSDbContext dbContext,
        JwtService jwtService,
        EncryptionService encryptionService,
        MemoryCacheService cacheService,
        ILogger<AuthService> logger)
    {
        _dbContext = dbContext;
        _jwtService = jwtService;
        _encryptionService = encryptionService;
        _cacheService = cacheService;
        _logger = logger;
    }

    /// <summary>
    /// API_AuthLogin - 用户登录
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="parameters">参数</param>
    /// <returns>登录结果</returns>
    public async Task<ApiResponse<LoginResponse>> API_AuthLogin(HttpContext context, Dictionary<string, object> parameters)
    {
        try
        {
            // 获取参数
            if (!parameters.TryGetValue("username", out var usernameObj) || 
                !parameters.TryGetValue("password", out var passwordObj))
            {
                return ApiResponse<LoginResponse>.CreateError("用户名或密码不能为空");
            }

            var username = usernameObj.ToString()!;
            var password = passwordObj.ToString()!;

            // 查找用户信息
            var user = await _dbContext.SysUsers
                .Where(u => !u.Del && u.UserID == username)
                .FirstOrDefaultAsync();

            if (user == null)
            {
                return ApiResponse<LoginResponse>.CreateError("操作失败，当前用户不存在!");
            }

            // 用户状态判断
            if (!user.IsActived)
            {
                return ApiResponse<LoginResponse>.CreateError("操作失败，当前用户已被停用，请联系管理员!");
            }

            // 账户锁定判断
            if (user.IsLocked && user.LastLockedDate.HasValue)
            {
                var lockTime = 10 - (DateTime.Now - user.LastLockedDate.Value).TotalMinutes;
                if (lockTime > 0)
                {
                    return ApiResponse<LoginResponse>.CreateError($"操作失败，当前用户已被限制，请在{lockTime:F0}分钟后再次尝试!");
                }
            }

            // 验证密码
            if (!_encryptionService.VerifyPassword(password, user.SecPW, "Tpit#123", "@yxw"))
            {
                return ApiResponse<LoginResponse>.CreateError("用户ID或密码错误!");
            }

            // 废弃之前的RefreshToken
            await _dbContext.SysAuthTokens
                .Where(t => !t.IsAbandoned && t.UserGuid == user.Guid)
                .ExecuteUpdateAsync(t => t.SetProperty(p => p.IsAbandoned, true));

            // 生成新的令牌
            var refreshToken = _jwtService.GenerateRefreshToken(user.Guid, user.UserID, user.UserName);
            var accessToken = _jwtService.GenerateAccessToken(user.Guid, user.UserID, user.UserName);

            // 记录新的RefreshToken
            var authToken = new SysAuthToken
            {
                UserGuid = user.Guid,
                UserID = user.UserID,
                RefreshToken = refreshToken,
                CreateDate = DateTime.Now,
                LastRefreshDate = DateTime.Now,
                IsAbandoned = false
            };

            _dbContext.SysAuthTokens.Add(authToken);
            await _dbContext.SaveChangesAsync();

            // 更新用户最后登录时间
            user.LastLoginDate = DateTime.Now;
            user.IsLocked = false;
            await _dbContext.SaveChangesAsync();

            var response = new LoginResponse
            {
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                Username = user.UserName,
                ExpiresAt = DateTime.Now.AddMinutes(120)
            };

            return ApiResponse<LoginResponse>.CreateSuccess(response, "登录成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户登录时发生错误");
            return ApiResponse<LoginResponse>.CreateError("登录失败，请稍后重试");
        }
    }

    /// <summary>
    /// API_AuthLogout - 用户登出
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="parameters">参数</param>
    /// <returns>登出结果</returns>
    public async Task<ApiResponse<object>> API_AuthLogout(HttpContext context, Dictionary<string, object> parameters)
    {
        try
        {
            if (!parameters.TryGetValue("token", out var tokenObj))
            {
                return ApiResponse<object>.CreateError("令牌不能为空");
            }

            var token = tokenObj.ToString()!;

            // 废弃RefreshToken
            await _dbContext.SysAuthTokens
                .Where(t => !t.IsAbandoned && t.RefreshToken == token)
                .ExecuteUpdateAsync(t => t.SetProperty(p => p.IsAbandoned, true));

            return ApiResponse<object>.CreateSuccess(null, "登出成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户登出时发生错误");
            return ApiResponse<object>.CreateError("登出失败，请稍后重试");
        }
    }

    /// <summary>
    /// API_AuthRefreshToken - 刷新令牌
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="parameters">参数</param>
    /// <returns>新的访问令牌</returns>
    public async Task<ApiResponse<string>> API_AuthRefreshToken(HttpContext context, Dictionary<string, object> parameters)
    {
        try
        {
            if (!parameters.TryGetValue("token", out var tokenObj) || string.IsNullOrEmpty(tokenObj.ToString()))
            {
                context.Response.StatusCode = 400;
                return ApiResponse<string>.CreateError("登录认证异常，请重新登录");
            }

            var token = tokenObj.ToString()!;
            var claims = _jwtService.DecodeToken(token);

            if (!claims.ContainsKey("sub"))
            {
                context.Response.StatusCode = 400;
                return ApiResponse<string>.CreateError("登录认证过期，请重新登录");
            }

            var userGuid = claims["sub"];
            var userId = claims["id"];
            var userName = claims["name"];

            // 生成新的访问令牌
            var newToken = _jwtService.GenerateAccessToken(userGuid, userId, userName);

            // 更新RefreshToken的最后刷新时间
            await _dbContext.SysAuthTokens
                .Where(t => !t.IsAbandoned && t.UserGuid == userGuid)
                .ExecuteUpdateAsync(t => t.SetProperty(p => p.LastRefreshDate, DateTime.Now));

            return ApiResponse<string>.CreateSuccess(newToken, "令牌刷新成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新令牌时发生错误");
            context.Response.StatusCode = 400;
            return ApiResponse<string>.CreateError("登录认证异常，请重新登录");
        }
    }

    /// <summary>
    /// API_AuthUserInfo - 获取用户信息
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="parameters">参数</param>
    /// <returns>用户信息</returns>
    public async Task<ApiResponse<UserInfoResponse>> API_AuthUserInfo(HttpContext context, Dictionary<string, object> parameters)
    {
        try
        {
            // 从Authorization头获取token
            if (!context.Request.Headers.ContainsKey("Authorization"))
            {
                context.Response.StatusCode = 401;
                return ApiResponse<UserInfoResponse>.CreateError("未授权访问");
            }

            var authHeader = context.Request.Headers["Authorization"].ToString();
            var token = authHeader.Substring("Bearer ".Length).Trim();
            var userGuid = _jwtService.GetUserGuidFromToken(token);

            if (string.IsNullOrEmpty(userGuid))
            {
                context.Response.StatusCode = 401;
                return ApiResponse<UserInfoResponse>.CreateError("用户信息获取失败!");
            }

            // 获取用户信息
            var user = await _dbContext.SysUsers
                .Where(u => !u.Del && u.Guid == userGuid)
                .FirstOrDefaultAsync();

            if (user == null)
            {
                context.Response.StatusCode = 401;
                return ApiResponse<UserInfoResponse>.CreateError("用户信息获取失败!");
            }

            var response = new UserInfoResponse
            {
                RealName = user.UserName,
                Email = user.Email,
                HomePath = "/workspace",
                ColSettings = null // TODO: 实现列设置功能
            };

            return ApiResponse<UserInfoResponse>.CreateSuccess(response, "获取用户信息成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户信息时发生错误");
            context.Response.StatusCode = 401;
            return ApiResponse<UserInfoResponse>.CreateError("用户信息获取失败!");
        }
    }

    /// <summary>
    /// API_AuthCodes - 获取用户权限代码
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="parameters">参数</param>
    /// <returns>权限代码列表</returns>
    public async Task<ApiResponse<JArray>> API_AuthCodes(HttpContext context, Dictionary<string, object> parameters)
    {
        try
        {
            // 从JWT中获取用户GUID
            var userGuid = GetUserGuidFromContext(context);
            if (string.IsNullOrEmpty(userGuid))
            {
                context.Response.StatusCode = 401;
                return ApiResponse<JArray>.CreateError("未授权访问");
            }

            // 获取用户角色
            var user = await _dbContext.SysUsers
                .Where(u => !u.Del && u.Guid == userGuid)
                .FirstOrDefaultAsync();

            if (user == null)
            {
                context.Response.StatusCode = 401;
                return ApiResponse<JArray>.CreateError("用户不存在");
            }

            var roleJa = JArray.Parse(user.UserRole);

            // 获取角色权限
            var roles = await _dbContext.SysRoles
                .Where(r => !r.Del && r.IsActived)
                .ToListAsync();

            var permJa = new JArray();
            foreach (var roleId in roleJa)
            {
                var role = roles.FirstOrDefault(r => r.Guid == roleId.ToString());
                if (role != null)
                {
                    var rolePermissions = JArray.Parse(role.Permission);
                    permJa = new JArray(permJa.Union(rolePermissions));
                }
            }

            return ApiResponse<JArray>.CreateSuccess(permJa, "获取权限成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户权限时发生错误");
            return ApiResponse<JArray>.CreateError("获取权限失败");
        }
    }

    /// <summary>
    /// 从HTTP上下文中获取用户GUID
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <returns>用户GUID</returns>
    private string? GetUserGuidFromContext(HttpContext context)
    {
        if (!context.Request.Headers.ContainsKey("Authorization"))
            return null;

        var authHeader = context.Request.Headers["Authorization"].ToString();
        if (!authHeader.StartsWith("Bearer "))
            return null;

        var token = authHeader.Substring("Bearer ".Length).Trim();
        return _jwtService.GetUserGuidFromToken(token);
    }
}
