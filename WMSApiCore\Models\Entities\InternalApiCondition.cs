using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMSApiCore.Models.Entities;

/// <summary>
/// 内部API条件表
/// </summary>
[Table("InternalApiCondition")]
public class InternalApiCondition
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 词类型
    /// </summary>
    [StringLength(16)]
    public string WordType { get; set; } = string.Empty;

    /// <summary>
    /// 词名称
    /// </summary>
    [StringLength(32)]
    public string WordName { get; set; } = string.Empty;

    /// <summary>
    /// 映射字段
    /// </summary>
    [StringLength(32)]
    public string MappingField { get; set; } = string.Empty;

    /// <summary>
    /// 操作符
    /// </summary>
    [StringLength(16)]
    public string Operator { get; set; } = string.Empty;

    /// <summary>
    /// API路径
    /// </summary>
    [StringLength(128)]
    public string ApiPath { get; set; } = string.Empty;
}
