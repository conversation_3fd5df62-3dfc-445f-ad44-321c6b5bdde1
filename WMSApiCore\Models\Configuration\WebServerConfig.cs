namespace WMSApiCore.Models.Configuration;

/// <summary>
/// Web服务器配置类
/// </summary>
public class WebServerConfig
{
    /// <summary>
    /// 监听IP地址
    /// </summary>
    public string IP { get; set; } = "0.0.0.0";

    /// <summary>
    /// 监听端口
    /// </summary>
    public int Port { get; set; } = 5000;

    /// <summary>
    /// HTTPS端口
    /// </summary>
    public int HttpsPort { get; set; } = 5001;

    /// <summary>
    /// 是否启用HTTPS
    /// </summary>
    public bool EnableHttps { get; set; } = false;
}
